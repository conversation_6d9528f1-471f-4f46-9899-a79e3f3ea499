﻿using HdProject.Application.Services.Interfaces.SaasPos.MarketingManagement.Evaluation;
using HdProject.Domain.Context.SaasPos.MarketingManagement.Evaluation.EvaluationTemplate;
using HdProject.Web.Core;
using Microsoft.AspNetCore.Mvc;

namespace HdProject.Web.Api.Core.Controllers.SaasPos.MarketingManagement.Evaluation
{
    /// <summary>
    /// 评价功能模板控制器接口
    /// </summary>
    public class EvaluationTemplateController : PublicControllerBase
    {
        private readonly IEvaluationTemplateService _evaluationTemplateService;
        private readonly ILogger<EvaluationTemplateController> _logger;

        public EvaluationTemplateController(IEvaluationTemplateService evaluationTemplateService, ILogger<EvaluationTemplateController> logger)
        {
            _evaluationTemplateService = evaluationTemplateService;
            _logger = logger;
        }

        /// <summary>
        /// 分页查询评价模板列表的接口
        /// </summary>
        /// <returns></returns>
        [HttpGet("GetAll")]
        public async Task<IActionResult> GetByRoomAssignShowings([FromQuery] EvaluationTemplateGetAllRequestDto request)
        {
            var result = await _evaluationTemplateService.GetAllAsync(request);
            return ApiPaged(result.Model, request);
        }

        /// <summary>
        /// 根据ID查询模板信息的接口
        /// </summary>
        /// <returns></returns>
        [HttpGet("GetById")]
        public async Task<IActionResult> GetByIdAssignShowings([FromQuery] EvaluationTemplateGetByIdRequestDto request)
        {
            var result = await _evaluationTemplateService.GetByIdAsync(request);
            return ApiData(result.Model);
        }

        /// <summary>
        /// 新增评价模板信息的接口
        /// </summary>
        /// <returns></returns>
        [HttpPost("Add")]
        public async Task<IActionResult> AssignShowingsAddValue([FromBody] EvaluationTemplateAddRequestDto request)
        {
            var result = await _evaluationTemplateService.AddAsync(request);
            if (result.Index > 0)
            {
                return ApiSuccess();
            }
            else
            {
                return ApiError();
            }
        }

        /// <summary>
        /// 修改评价模板信息的接口
        /// </summary>
        /// <returns></returns>
        [HttpPut("Update")]
        public async Task<IActionResult> AssignShowingsUpdateValue([FromBody] EvaluationTemplateUpdateRequestDto request)
        {
            var result = await _evaluationTemplateService.UpdateAsync(request);
            if (result.Index > 0)
            {
                return ApiSuccess();
            }
            else
            {
                return ApiError();
            }
        }

        /// <summary>
        /// 删除评价模板信息的接口
        /// </summary>
        /// <returns></returns>
        [HttpDelete("Delete")]
        public async Task<IActionResult> AssignShowingsDeletedValue([FromBody] EvaluationTemplateDeleteRequestDto request)
        {
            var result = await _evaluationTemplateService.DeleteAsync(request);
            if (result.Index > 0)
            {
                return ApiSuccess();
            }
            else
            {
                return ApiError();
            }
        }

    }
}
