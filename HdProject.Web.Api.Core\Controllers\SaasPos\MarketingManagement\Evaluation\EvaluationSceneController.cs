﻿using HdProject.Application.Services.Interfaces.SaasPos.MarketingManagement.Evaluation;
using HdProject.Web.Core;
using Microsoft.AspNetCore.Mvc;

namespace HdProject.Web.Api.Core.Controllers.SaasPos.MarketingManagement.Evaluation
{
    public class EvaluationSceneController: PublicControllerBase
    {
        private readonly IEvaluationSceneService _evaluationSceneService;
        private readonly ILogger<EvaluationSceneController> _logger;

        public EvaluationSceneController(IEvaluationSceneService evaluationSceneService, ILogger<EvaluationSceneController> logger)
        {
            _evaluationSceneService = evaluationSceneService;
            _logger = logger;
        }

        /// <summary>
        /// 分页查询评价模板列表的接口
        /// </summary>
        /// <returns></returns>
        [HttpGet("GetAll")]
        public async Task<IActionResult> GetByRoomAssignShowings()
        {
            var result = await _evaluationSceneService.GetAllAsync();
            return ApiData(result.Model);
        }

    }
}
