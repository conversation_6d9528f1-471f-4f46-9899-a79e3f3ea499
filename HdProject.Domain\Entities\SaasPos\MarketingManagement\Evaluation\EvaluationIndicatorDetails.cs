﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using SqlSugar;

namespace HdProject.Domain.Entities.SaasPos.MarketingManagement.Evaluation
{
    /// <summary>
    /// 评价指标明细表模型
    /// </summary>
    [SugarTable("MK_EvaluationIndicatorDetails")]
    public class EvaluationIndicatorDetails
    {
        /// <summary>
        /// ID，自增主键
        /// </summary>
        [SugarColumn(ColumnName = "DetailsID", IsPrimaryKey = true, IsIdentity = true)]
        public int DetailsID { get; set; }
        public int IndicatorID { get; set; }
        public string? DetailsIDName { get; set; }

    }
}
