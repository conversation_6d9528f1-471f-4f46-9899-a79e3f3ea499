﻿using Furion.DataValidation;
using HdProject.Application.Services.Interfaces;
using HdProject.Application.Services.Interfaces.MarketingActivityManagement;
using HdProject.Domain.Context.MarketingActivityManagement;
using HdProject.Domain.DTOs.GrouponBase;
using HdProject.Domain.DTOs.MarketingActivityManagement;
using HdProject.Domain.Entities;
using HdProject.Domain.Entities.GroupBase;
using HdProject.Domain.Entities.Rms;
using HdProject.Domain.Entities.SaasPos;
using HdProject.Domain.Interfaces;
using Microsoft.AspNetCore.Http.HttpResults;
using NetTaste;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using static Dm.net.buffer.ByteArrayBuffer;
using static HdProject.Domain.Context.MarketingActivityManagement.StudentCertificationContext;
using static HdProject.Domain.DTOs.MarketingActivityManagement.StudentCertificationDto;
using static Microsoft.EntityFrameworkCore.DbLoggerCategory;
using static System.Runtime.InteropServices.JavaScript.JSType;

namespace HdProject.Application.Services.MarketingActivityManagement
{

    public class StudentCertificationService : IStudentCertificationService
    {
        private readonly IWeChatCloudDbService _weChatCloudDbService;
        private readonly IRepositorySaas<StudentVerification> _studentVerificationRepository;
        private readonly IJwtService _jwtService;
        private readonly IRepositorySaas<StudentConsumption> _studentConsumptionRepository;
        public StudentCertificationService(IWeChatCloudDbService weChatCloudDbService,
            IRepositorySaas<StudentVerification> studentVerificationRepository,
            IJwtService jwtService,
            IRepositorySaas<StudentConsumption> studentConsumptionRepository
            )
        {
            _weChatCloudDbService = weChatCloudDbService;
            _studentVerificationRepository = studentVerificationRepository;
            _jwtService = jwtService;
            _studentConsumptionRepository = studentConsumptionRepository;
        }



        /// <summary>
        /// 微信大学生认证
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        /// <exception cref="NotImplementedException"></exception>
        public async Task<WxStudentAuthenticationDto> WxStudentAuthenticationRecord(WxStudentAuthenticationContext context)
        {


            _studentVerificationRepository.BeginTran();
            try
            {

                //1.查询现有记录
                var studentVerificationList = await _studentVerificationRepository.GetFirstAsync(w => w.OpenId == context.OpenId);
                //1.手机号为空
                if (string.IsNullOrEmpty(context.Tel))
                {
                    //1.1手机号为空，数据库不存在记录
                    if (!string.IsNullOrEmpty(context.OpenId) && !string.IsNullOrEmpty(context.WxStudentCheckCode) && studentVerificationList == null)
                    {

                        var verificationResult = await VerifyWeChatStudent(context.OpenId, context.WxStudentCheckCode);
                        if (verificationResult.Status != 0) // 认证失败
                        {
                            return verificationResult; // 直接返回错误信息
                        }

                        var newRecord = new StudentVerification
                        {
                            OpenId = context.OpenId,
                            Tel = null,
                            //Sign = "",//认证签名，用于存储识别者身份
                            UseSummary = 0,//使用优惠汇总
                            LastTime = DateTime.Now,//最后认证时间
                            ValidTime = DateTime.Now.AddMonths(1),///认证有效时间
                            InTime = DateTime.Now//创建时间
                        };
                        await _studentVerificationRepository.InsertAsync(newRecord);
                        _studentVerificationRepository.CommitTran();
                        return new WxStudentAuthenticationDto
                        {
                            Status = 6,
                            Success = true,
                            Token = null,
                            Message = "微信学生认证成功但是缺失手机号"
                        };
                    }
                    //1.2手机号为空，数据库存在记录
                    else if (!string.IsNullOrEmpty(context.OpenId) && !string.IsNullOrEmpty(context.WxStudentCheckCode) && studentVerificationList != null)
                    {
                        return new WxStudentAuthenticationDto
                        {
                            Status = 6,
                            Success = true,
                            Token = null,
                            Message = "微信学生认证成功但是缺失手机号"
                        };
                    }
                }
                else  //2.手机号不为空
                {
                    //2.1数据库有记录,手机号为空
                    if (!string.IsNullOrEmpty(context.OpenId) && !string.IsNullOrEmpty(context.WxStudentCheckCode) && studentVerificationList != null && studentVerificationList.OpenId == context.OpenId && string.IsNullOrEmpty(studentVerificationList.Tel))
                    {
                        var token = GenerateToken(context.OpenId);
                        studentVerificationList.Tel = context.Tel;//电话号
                        await _studentVerificationRepository.UpdateAsync(studentVerificationList);
                        _studentVerificationRepository.CommitTran();
                        return new WxStudentAuthenticationDto
                        {
                            Status = 0,
                            Success = true,
                            Token = token,
                            Message = "认证成功"
                        };
                    }
                    //2.2数据库有记录,手机号不为空
                    else if (!string.IsNullOrEmpty(context.OpenId) && !string.IsNullOrEmpty(context.WxStudentCheckCode) && studentVerificationList != null && studentVerificationList.OpenId == context.OpenId && !string.IsNullOrEmpty(studentVerificationList.Tel))
                    {
                        var verificationResult = await VerifyWeChatStudent(context.OpenId, context.WxStudentCheckCode);
                        if (verificationResult.Status != 0) // 认证失败
                        {
                            return verificationResult; // 直接返回错误信息
                        }
                        var token = GenerateToken(context.OpenId);

                        studentVerificationList.Tel = context.Tel;//电话号
                        studentVerificationList.LastTime = DateTime.Now;//最后认证时间
                        studentVerificationList.ValidTime = DateTime.Now.AddMonths(1);///认证有效时间

                        await _studentVerificationRepository.UpdateAsync(studentVerificationList);

                        _studentVerificationRepository.CommitTran();
                        return new WxStudentAuthenticationDto
                        {
                            Status = 0,
                            Success = true,
                            Token = token,
                            Message = "认证成功"
                        };
                    }
                    //2.3数据库无记录
                    else if (!string.IsNullOrEmpty(context.OpenId) && !string.IsNullOrEmpty(context.WxStudentCheckCode) && studentVerificationList == null)
                    {

                        var verificationResult = await VerifyWeChatStudent(context.OpenId, context.WxStudentCheckCode);
                        if (verificationResult.Status != 0) // 认证失败
                        {
                            return verificationResult; // 直接返回错误信息
                        }

                        var newRecord = new StudentVerification
                        {
                            OpenId = context.OpenId,
                            Tel = context.Tel,
                            //Sign = "",//认证签名，用于存储识别者身份
                            UseSummary = 0,//使用优惠汇总
                            LastTime = DateTime.Now,//最后认证时间
                            ValidTime = DateTime.Now.AddMonths(1),///认证有效时间
                            InTime = DateTime.Now//创建时间
                        };
                        await _studentVerificationRepository.InsertAsync(newRecord);
                        _studentVerificationRepository.CommitTran();
                        var token = GenerateToken(context.OpenId);
                        return new WxStudentAuthenticationDto
                        {
                            Status = 0,
                            Success = true,
                            Token = token,
                            Message = "认证成功"
                        };


                    }
                }
                return new WxStudentAuthenticationDto
                {

                    Success = false,

                    Message = "微信大学生认证失败"
                };
            }
            catch (Exception ex)
            {
                _studentVerificationRepository.RollbackTran();
                return new WxStudentAuthenticationDto
                {
                    Success = false,
                    Message = $"微信大学生认证失败: {ex.Message}"
                };
            }

        }
        /// <summary>
        /// 本地系统学生认证
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        /// <exception cref="NotImplementedException"></exception>
        public async Task<LocalStudentAuthenticationDto> LocalStudentAuthenticationRecord(LocalStudentAuthenticationContext context)
        {
            try
            {
                //////要判断活动有没有过期
                //DateTime activityStartTime = new DateTime(DateTime.Now.Year, 9, 1, 0, 0, 0);
                //DateTime activityEndTime = new DateTime(DateTime.Now.Year, 9, 30, 23, 59, 59);
                //if (DateTime.Now < activityStartTime)
                //{
                //    return new LocalStudentAuthenticationDto
                //    {
                //        Status = 3,
                //        Success = false,
                //        Message = "优惠活动未开始"
                //    };
                //}
                //if (DateTime.Now > activityEndTime)
                //{
                //    return new LocalStudentAuthenticationDto
                //    {
                //        Status = 3,
                //        Success = false,
                //        Message = "优惠活动已结束"
                //    };
                //}

                var studentVerificationList = await _studentVerificationRepository.GetFirstAsync(w => w.OpenId == context.OpenId);
                var result = new LocalStudentAuthenticationDto();

                if (studentVerificationList == null)
                {
                    result = new LocalStudentAuthenticationDto
                    {
                        Status = 4,
                        Success = false,
                        Message = "该学生在系统未认证"

                    };
                    return result;
                }

                if (studentVerificationList.ValidTime < DateTime.Now)
                {

                    result = new LocalStudentAuthenticationDto
                    {
                        Status = 5,
                        Success = false,
                        Message = "认证已过期，请重新认证"
                    };
                    return result;
                }
                if (string.IsNullOrEmpty(studentVerificationList.Tel))
                {
                    result = new LocalStudentAuthenticationDto
                    {
                        Status = 6,
                        Success = false,
                        Message = "微信学生认证成功但是缺失手机号"

                    };
                    return result;
                }
                //已认证和未过期

                var token = GenerateToken(context.OpenId);

                result = new LocalStudentAuthenticationDto
                {
                    Status = 0,
                    Success = true,
                    Token = token,
                    Message = "已认证"
                };
                return result;
            }
            catch (Exception ex)
            {
                throw new Exception("系统学生认证失败");
            }

        }
        /// <summary>
        /// 学生消费记录
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        /// <exception cref="NotImplementedException"></exception>
        public async Task<StudentConsumptionDto> StudentConsumptionRecord(StudentConsumptionContext context)
        {
            var result = new StudentConsumptionDto();
            var studentConsumptionList = await _studentConsumptionRepository.GetListAsync(w => w.OpenId == context.OpenId);
            bool hasConsumedToday = studentConsumptionList.Any(r => r.ConsumptionTime.Date == DateTime.Today);
            //一天只能使用一次
            if (hasConsumedToday)
            {
               return result = new StudentConsumptionDto
               {
                    Status = false,
                    Message = "一天只能使用一次"
               };
            }

            var studentVerificationList = await _studentVerificationRepository.GetFirstAsync(w => w.OpenId == context.OpenId);
            _studentVerificationRepository.BeginTran();
            _studentConsumptionRepository.BeginTran();
            try
            {
                studentVerificationList.UseSummary = studentVerificationList.UseSummary + 1;//使用次数加1
            
                await _studentVerificationRepository.UpdateAsync(studentVerificationList);
                //消费记录
                var newRecord = new StudentConsumption
                {
                    OpenId = context.OpenId,
                    ConsumptionTime = DateTime.Now,
                    InvNo = context.InvNo,
                    ShopId = context.ShopId,
                    RmNo = context.RmNo,
                    CustomNo = Guid.NewGuid(),
                    UseSummary = studentVerificationList.UseSummary
                };
                await _studentConsumptionRepository.InsertAsync(newRecord);

                _studentVerificationRepository.CommitTran();
                _studentConsumptionRepository.CommitTran();
                return result = new StudentConsumptionDto
                {
                    Status = true,
                    Message = "成功"
                };
            }
            catch (Exception ex)
            {
                _studentVerificationRepository.RollbackTran();
                _studentConsumptionRepository.RollbackTran();
                return result = new StudentConsumptionDto
                {
                    Status = false,
                    Message = "学生消费记录插入失败"
                };
                throw new Exception();
            }
        }


        private async Task<WxStudentAuthenticationDto> VerifyWeChatStudent(string OpenId, string WxStudentCheckCode)
        {
            //调用微信学生认证
            var result = await _weChatCloudDbService.QuickCheckStudentIdentity(OpenId, WxStudentCheckCode);
            var response = JObject.Parse(result);


            int errCode = (int)response["errcode"];

            if (errCode != 0)
            {
                string errMsg = (string)response["errmsg"];
                return new WxStudentAuthenticationDto
                {
                    Status = 1,
                    Success = false,
                    Message = $"微信学生认证失败: {errMsg}"
                };
            }
            int bindStatus = (int)response["bind_status"];
            bool isStudent = (bool)response["is_student"];
            if (bindStatus != 3 || !isStudent)
            {
                return new WxStudentAuthenticationDto
                {
                    Status = 2,
                    Success = false,
                    Message = "学生身份验证未通过"
                };
            }
            return new WxStudentAuthenticationDto
            {
                Status = 0

            };
        }
        private string GenerateToken(string openId)
        {
            long times = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
            var qrCodeData = $"{openId}{times}";
            return _jwtService.GenerateEncryptedQrCodeToken(qrCodeData);
        }


    }
}
