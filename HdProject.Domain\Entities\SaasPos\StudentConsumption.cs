﻿using SqlSugar;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace HdProject.Domain.Entities.SaasPos
{
    public class StudentConsumption
    {
        [SugarColumn(IsPrimaryKey = true, IsIdentity = true)]
        public int Id { get; set; }
        public string OpenId { get; set; }
        public DateTime ConsumptionTime { get; set; }
        public string InvNo { get; set; }
        public int ShopId { get; set; }
        public string RmNo { get; set; }
        public Guid CustomNo { get; set; }
        public int UseSummary { get; set; }
    }
}
