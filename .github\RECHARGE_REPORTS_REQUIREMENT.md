# 充值报表系统需求文档

## 1. 业务背景与目标

### 1.1 业务背景
基于 `Fact_Daily_Shop_Summary` 表中的储值相关数据，需要开发充值报表系统，为运营团队提供充值业务的数据分析和决策支持。

### 1.2 业务目标
- 提供充值总额的统计分析，包括渠道分布和趋势分析
- 提供充值档位的批次分析，了解用户充值行为偏好
- 支持多维度查询和数据导出功能
- 为运营决策提供数据支撑

### 1.3 数据源
- 主表：`Fact_Daily_Shop_Summary`
- 储值相关字段：21个字段，涵盖充值总额、渠道分布、8个档位的批次和金额数据

## 2. 功能模块设计

### 2.1 模块划分
本需求包含两个主要报表模块：

#### 2.1.1 充值总额报表模块 (RechargeAmountReports)
- **功能描述**: 分析充值总额、渠道分布、占比等汇总数据
- **核心指标**: 
  - 充值总额 (DailyRecharge_Total)
  - 新美大渠道充值额 (DailyRecharge_Meituan)
  - 新美大充值占比 (DailyRecharge_Meituan_Ratio)
- **分析维度**: 时间、门店、渠道

#### 2.1.2 充值档位报表模块 (RechargeTierReports)
- **功能描述**: 分析不同充值档位的批次数和金额分布
- **核心指标**: 8个档位的批次数和总金额
- **档位划分**: 
  - Tier1: 1-499元
  - Tier2: 500-999元
  - Tier3: 1000-1999元
  - Tier4: 2000-2999元
  - Tier5: 3000-4999元
  - Tier6: 5000-9999元
  - Tier7: 10000-19999元
  - Tier8: 20000元及以上

## 3. 接口设计规范

### 3.1 控制器命名
- `RechargeAmountReportsController` - 充值总额报表控制器
- `RechargeTierReportsController` - 充值档位报表控制器

### 3.2 接口路由设计
遵循项目标准API路由规范：

#### 3.2.1 充值总额报表接口
```
GET  /api/Reports/GetRechargeAmountSummary      - 获取充值总额汇总数据
GET  /api/Reports/ExportRechargeAmountSummary   - 导出充值总额汇总报表
```

#### 3.2.2 充值档位报表接口
```
GET  /api/Reports/GetRechargeTierSummary        - 获取充值档位汇总数据
GET  /api/Reports/ExportRechargeTierSummary     - 导出充值档位汇总报表
```

## 4. 数据传输对象 (DTO) 设计

### 4.1 查询请求DTO

#### 4.1.1 基础查询DTO
```csharp
/// <summary>
/// 充值报表查询基础DTO
/// </summary>
public class RechargeReportQueryDto : Pagination
{
    /// <summary>
    /// 开始日期
    /// </summary>
    [Required]
    public DateTime StartDate { get; set; }
    
    /// <summary>
    /// 结束日期
    /// </summary>
    [Required]
    public DateTime EndDate { get; set; }
    
    /// <summary>
    /// 门店ID列表 (可选，为空时查询所有门店)
    /// </summary>
    public List<int>? ShopIds { get; set; }
    
    /// <summary>
    /// 门店名称关键字 (可选)
    /// </summary>
    public string? ShopNameKeyword { get; set; }
    
    /// <summary>
    /// 排序字段
    /// </summary>
    public string? OrderBy { get; set; } = "Date";
    
    /// <summary>
    /// 排序方向 (asc/desc)
    /// </summary>
    public string? OrderDirection { get; set; } = "desc";
}
```

#### 4.1.2 充值总额查询DTO
```csharp
/// <summary>
/// 充值总额报表查询DTO
/// </summary>
public class RechargeAmountQueryDto : RechargeReportQueryDto
{
    /// <summary>
    /// 最小充值金额过滤
    /// </summary>
    public decimal? MinAmount { get; set; }
    
    /// <summary>
    /// 最大充值金额过滤
    /// </summary>
    public decimal? MaxAmount { get; set; }
    
    /// <summary>
    /// 是否只查询新美大渠道
    /// </summary>
    public bool? OnlyMeituan { get; set; }
}
```

#### 4.1.3 充值档位查询DTO
```csharp
/// <summary>
/// 充值档位报表查询DTO
/// </summary>
public class RechargeTierQueryDto : RechargeReportQueryDto
{
    /// <summary>
    /// 指定查询的档位 (1-8，为空时查询所有档位)
    /// </summary>
    public List<int>? TierLevels { get; set; }
    
    /// <summary>
    /// 最小批次数过滤
    /// </summary>
    public int? MinBatchCount { get; set; }
}
```

### 4.2 响应数据DTO

#### 4.2.1 充值总额汇总DTO
```csharp
/// <summary>
/// 充值总额汇总数据DTO
/// </summary>
public class RechargeAmountSummaryDto
{
    /// <summary>
    /// 日期
    /// </summary>
    public DateTime Date { get; set; }
    
    /// <summary>
    /// 门店ID
    /// </summary>
    public int ShopId { get; set; }
    
    /// <summary>
    /// 门店名称
    /// </summary>
    public string ShopName { get; set; } = string.Empty;
    
    /// <summary>
    /// 当天充值总额
    /// </summary>
    public decimal DailyRechargeTotal { get; set; }
    
    /// <summary>
    /// 新美大渠道充值额
    /// </summary>
    public decimal DailyRechargeMeituan { get; set; }
    
    /// <summary>
    /// 新美大充值占比
    /// </summary>
    public decimal DailyRechargeMeituanRatio { get; set; }
    
    /// <summary>
    /// 其他渠道充值额
    /// </summary>
    public decimal DailyRechargeOther => DailyRechargeTotal - DailyRechargeMeituan;
    
    /// <summary>
    /// 其他渠道充值占比
    /// </summary>
    public decimal DailyRechargeOtherRatio => 1 - DailyRechargeMeituanRatio;
}
```

#### 4.2.2 充值档位汇总DTO
```csharp
/// <summary>
/// 充值档位汇总数据DTO
/// </summary>
public class RechargeTierSummaryDto
{
    /// <summary>
    /// 日期
    /// </summary>
    public DateTime Date { get; set; }
    
    /// <summary>
    /// 门店ID
    /// </summary>
    public int ShopId { get; set; }
    
    /// <summary>
    /// 门店名称
    /// </summary>
    public string ShopName { get; set; } = string.Empty;
    
    /// <summary>
    /// 各档位数据
    /// </summary>
    public List<RechargeTierDataDto> TierData { get; set; } = new();
    
    /// <summary>
    /// 总批次数
    /// </summary>
    public int TotalBatchCount => TierData.Sum(x => x.BatchCount);
    
    /// <summary>
    /// 总金额
    /// </summary>
    public decimal TotalAmount => TierData.Sum(x => x.TotalAmount);
}

/// <summary>
/// 充值档位数据DTO
/// </summary>
public class RechargeTierDataDto
{
    /// <summary>
    /// 档位级别 (1-8)
    /// </summary>
    public int TierLevel { get; set; }
    
    /// <summary>
    /// 档位范围描述
    /// </summary>
    public string TierRange { get; set; } = string.Empty;
    
    /// <summary>
    /// 批次数
    /// </summary>
    public int BatchCount { get; set; }
    
    /// <summary>
    /// 总金额
    /// </summary>
    public decimal TotalAmount { get; set; }
    
    /// <summary>
    /// 平均单笔金额
    /// </summary>
    public decimal AverageAmount => BatchCount > 0 ? TotalAmount / BatchCount : 0;
}
```

#### 4.2.3 导出请求DTO
```csharp
/// <summary>
/// 报表导出请求DTO
/// </summary>
public class ReportExportRequestDto
{
    /// <summary>
    /// 查询条件 (JSON字符串)
    /// </summary>
    [Required]
    public string QueryJson { get; set; } = string.Empty;
    
    /// <summary>
    /// 导出格式 (excel/csv)
    /// </summary>
    public string Format { get; set; } = "excel";
    
    /// <summary>
    /// 文件名 (可选)
    /// </summary>
    public string? FileName { get; set; }
}
```

## 5. 数据库实体映射

### 5.1 实体定义
```csharp
/// <summary>
/// 日店汇总表实体 (对应 Fact_Daily_Shop_Summary)
/// </summary>
public class FactDailyShopSummary
{
    /// <summary>
    /// 主键ID
    /// </summary>
    public int Id { get; set; }
    
    /// <summary>
    /// 统计日期
    /// </summary>
    public DateTime Date { get; set; }
    
    /// <summary>
    /// 门店ID
    /// </summary>
    public int ShopId { get; set; }
    
    /// <summary>
    /// 门店名称
    /// </summary>
    public string ShopName { get; set; } = string.Empty;
    
    // --- 储值相关指标 ---
    
    /// <summary>
    /// 当天充值总额 (线上+线下)
    /// </summary>
    public decimal DailyRecharge_Total { get; set; }
    
    /// <summary>
    /// 当天新美大渠道充值总额
    /// </summary>
    public decimal DailyRecharge_Meituan { get; set; }
    
    /// <summary>
    /// 当天新美大充值占比
    /// </summary>
    public decimal DailyRecharge_Meituan_Ratio { get; set; }
    
    // 充值档位 1-8 的批次数和总金额
    public int RechargeTier1_BatchCount { get; set; }
    public decimal RechargeTier1_TotalAmount { get; set; }
    
    public int RechargeTier2_BatchCount { get; set; }
    public decimal RechargeTier2_TotalAmount { get; set; }
    
    public int RechargeTier3_BatchCount { get; set; }
    public decimal RechargeTier3_TotalAmount { get; set; }
    
    public int RechargeTier4_BatchCount { get; set; }
    public decimal RechargeTier4_TotalAmount { get; set; }
    
    public int RechargeTier5_BatchCount { get; set; }
    public decimal RechargeTier5_TotalAmount { get; set; }
    
    public int RechargeTier6_BatchCount { get; set; }
    public decimal RechargeTier6_TotalAmount { get; set; }
    
    public int RechargeTier7_BatchCount { get; set; }
    public decimal RechargeTier7_TotalAmount { get; set; }
    
    public int RechargeTier8_BatchCount { get; set; }
    public decimal RechargeTier8_TotalAmount { get; set; }
}
```

## 6. 服务层设计

### 6.1 服务接口
```csharp
/// <summary>
/// 充值总额报表服务接口
/// </summary>
public interface IRechargeAmountReportService
{
    Task<List<RechargeAmountSummaryDto>> GetSummaryAsync(RechargeAmountQueryDto query);
    Task<List<RechargeAmountSummaryDto>> GetDetailAsync(RechargeAmountQueryDto query);
    Task<byte[]> ExportSummaryAsync(RechargeAmountQueryDto query, string format = "excel");
    Task<byte[]> ExportDetailAsync(RechargeAmountQueryDto query, string format = "excel");
    Task<int> GetSummaryCountAsync(RechargeAmountQueryDto query);
}

/// <summary>
/// 充值档位报表服务接口
/// </summary>
public interface IRechargeTierReportService
{
    Task<List<RechargeTierSummaryDto>> GetSummaryAsync(RechargeTierQueryDto query);
    Task<List<RechargeTierSummaryDto>> GetDetailAsync(RechargeTierQueryDto query);
    Task<byte[]> ExportSummaryAsync(RechargeTierQueryDto query, string format = "excel");
    Task<byte[]> ExportDetailAsync(RechargeTierQueryDto query, string format = "excel");
    Task<int> GetSummaryCountAsync(RechargeTierQueryDto query);
}
```

## 7. 技术实现要点

### 7.1 查询优化
- 使用索引优化日期和门店查询
- 对大数据量查询实施分页
- 缓存常用查询结果

### 7.2 导出功能
- 支持Excel和CSV格式导出
- 大数据量导出使用流式处理
- 提供下载进度反馈

### 7.3 数据聚合
- 汇总查询按日期和门店分组
- 计算各种占比和平均值
- 提供环比、同比分析

## 8. 开发任务分解

### 8.1 第一阶段：基础架构搭建
- [ ] 创建 DTO 类定义
- [ ] 创建实体类和数据库映射
- [ ] 创建服务接口和基础实现
- [ ] 创建控制器骨架

### 8.2 第二阶段：充值总额报表功能
- [ ] 实现 RechargeAmountReportService
- [ ] 实现 RechargeAmountReportsController
- [ ] 添加查询和导出功能
- [ ] 编写单元测试

### 8.3 第三阶段：充值档位报表功能
- [ ] 实现 RechargeTierReportService
- [ ] 实现 RechargeTierReportsController
- [ ] 添加查询和导出功能
- [ ] 编写单元测试

### 8.4 第四阶段：优化和测试
- [ ] 性能优化和查询调优
- [ ] 集成测试
- [ ] 文档完善
- [ ] 部署和验证

## 9. 验收标准

### 9.1 功能验收
- ✅ 所有8个接口正常响应
- ✅ 查询结果数据准确性验证
- ✅ 导出功能正常，文件格式正确
- ✅ 分页和排序功能正常

### 9.2 性能验收
- ✅ 查询响应时间 < 3秒
- ✅ 导出功能支持10万条数据
- ✅ 并发支持50个用户同时访问

### 9.3 安全验收
- ✅ 接口权限控制正确
- ✅ 输入参数验证完整
- ✅ 敏感数据保护

---

**文档版本**: 1.0  
**创建时间**: 2025-08-25  
**创建者**: HdProject开发团队  
**项目**: HdProject 充值报表系统  
**模块**: Reports/Recharge
