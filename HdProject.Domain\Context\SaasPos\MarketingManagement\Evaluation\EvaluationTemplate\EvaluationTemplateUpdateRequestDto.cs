﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using HdProject.Domain.Context.SaasPos.MarketingManagement.Evaluation.EvaluationContentConfig;
using HdProject.Domain.Context.SaasPos.MarketingManagement.Evaluation.EvaluationIndicator;
using HdProject.Domain.Context.SaasPos.MarketingManagement.Evaluation.EvaluationScene;
using HdProject.Domain.Context.SaasPos.MarketingManagement.Evaluation.TemplateIndicatorRelation;

namespace HdProject.Domain.Context.SaasPos.MarketingManagement.Evaluation.EvaluationTemplate
{
    /// <summary>
    /// 修改
    /// </summary>
    public  class EvaluationTemplateUpdateRequestDto
    {
        public EvaluationTemplateCreateDto Model { get; set; }
        /// <summary>
        /// 场景
        /// </summary>
        public List<EvaluationSceneCreateDto> sceneCreateDtos { get; set; }
        /// <summary>
        /// 评价指标列表
        /// </summary>
        public List<EvaluationIndicatorCreateDto> IndicatorCreateDto { get; set; }
        /// <summary>
        /// 评价内容配置
        /// </summary>
        public EvaluationContentConfigCreateDto ConfigCreateDto { get; set; }


    }
}
