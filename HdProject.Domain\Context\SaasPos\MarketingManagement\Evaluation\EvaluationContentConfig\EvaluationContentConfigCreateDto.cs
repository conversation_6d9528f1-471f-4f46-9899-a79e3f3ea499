﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace HdProject.Domain.Context.SaasPos.MarketingManagement.Evaluation.EvaluationContentConfig
{
    public class EvaluationContentConfigCreateDto
    {
        public int ContentConfigID { get; set; }

        /// <summary>
        /// 模板外键
        /// </summary>
        public int TemplateID { get; set; }

        /// <summary>
        /// 是否文字评价
        /// </summary>
        public bool IsTextEvaluation { get; set; }
        /// <summary>
        /// 是否图片评价
        /// </summary>
        public bool IsImageEvaluation { get; set; }

        /// <summary>
        /// 是否视频评价
        /// </summary>
        public bool IsVideoEvaluation { get; set; }
        /// <summary>
        /// 是否必填
        /// </summary>
        public bool IsRequired { get; set; }
        /// <summary>
        /// 文字评价引导内容
        /// </summary>
        public string? GuideText { get; set; }
    }
}
