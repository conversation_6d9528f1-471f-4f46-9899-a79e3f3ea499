﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using HdProject.Domain.Context.SaasPos.MarketingManagement.Evaluation.EvaluationIndicatorDetails;

namespace HdProject.Domain.Context.SaasPos.MarketingManagement.Evaluation.EvaluationIndicator
{
    /// <summary>
    /// 模板指标
    /// </summary>
    public class EvaluationIndicatorDto
    {
        public int IndicatorId { get; set; }

        /// <summary>
        /// 指标名称，如菜品、服务
        /// </summary>
        public string IndicatorName { get; set; }

        /// <summary>
        /// 指标类型，比如星级评分、文字描述等
        /// </summary>
        public string? IndicatorType { get; set; }

        /// <summary>
        /// 指标描述，对该指标的说明
        /// </summary>
        public string? IndicatorDescription { get; set; }
        /// <summary>
        /// 是否必填
        /// </summary>
        public bool? IsRequired { get; set; }

        /// <summary>
        /// 顺序号
        /// </summary>
        public int SortOrder { get; set; }
        /// <summary>
        /// 指标明细集合
        /// </summary>
        public List<EvaluationIndicatorDetailsDto> detailsDtos { get; set; }
    }
}
