﻿using HdProject.Domain.Context.MarketingActivityManagement;
using HdProject.Domain.DTOs.MarketingActivityManagement;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using static HdProject.Domain.Context.MarketingActivityManagement.MarketingActivityContext;
using static HdProject.Domain.Context.MarketingActivityManagement.StudentCertificationContext;
using static HdProject.Domain.DTOs.MarketingActivityManagement.StudentCertificationDto;

namespace HdProject.Application.Services.Interfaces.MarketingActivityManagement
{
    public interface IStudentCertificationService
    {
        /// <summary>
        /// 微信大学生认证
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        Task<WxStudentAuthenticationDto> WxStudentAuthenticationRecord(WxStudentAuthenticationContext context);
        /// <summary>
        /// 本地系统大学生认证
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        Task<LocalStudentAuthenticationDto> LocalStudentAuthenticationRecord(LocalStudentAuthenticationContext context);
        /// <summary>
        /// 学生消费记录
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        Task<StudentConsumptionDto> StudentConsumptionRecord(StudentConsumptionContext context);
    }
}
