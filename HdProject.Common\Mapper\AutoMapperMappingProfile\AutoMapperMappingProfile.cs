﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using AutoMapper;
using HdProject.Domain.Context.MainFood.Room;
using HdProject.Domain.Context.SaasPos.Commission.AssignEmployee;
using HdProject.Domain.Context.SaasPos.Commission.AssignRecommend;
using HdProject.Domain.Context.SaasPos.Commission.AssignShowings;
using HdProject.Domain.Context.SaasPos.ExternalGroupBuying;
using HdProject.Domain.Context.SaasPos.MarketingManagement.Evaluation.EvaluationTemplate;
using HdProject.Domain.Context.SaasPos.MaterialManagement.MMAdCampaign;
using HdProject.Domain.Context.SaasPos.MaterialManagement.MMDevice;
using HdProject.Domain.Context.SaasPos.MaterialManagement.MMFile;
using HdProject.Domain.Context.SaasPos.MaterialManagement.MMLayout;
using HdProject.Domain.Context.SaasPos.MaterialManagement.MMPlaylist;
using HdProject.Domain.Context.SongBase.WallPainting.WPTopicI;
using HdProject.Domain.Context.SongBase.WallPainting.WPTopicIFile;
using HdProject.Domain.Context.SongBase.WallPainting.WPTopicITemplate;
using HdProject.Domain.Entities.MainFood.Room;
using HdProject.Domain.Entities.SaasPos.Commission;
using HdProject.Domain.Entities.SaasPos.ExternalGroupBuying;
using HdProject.Domain.Entities.SaasPos.MarketingManagement.Evaluation;
using HdProject.Domain.Entities.SaasPos.MaterialManagement;
using HdProject.Domain.Entities.SongBase.WallPainting;
using static System.Runtime.InteropServices.JavaScript.JSType;

namespace HdProject.Common.Mapper.AutoMapperMappingProfile
{
    /// <summary>
    /// AutoMapper映射配置文件
    /// </summary>
    public class AutoMapperMappingProfile : Profile
    {
        /// <summary>
        /// 添加映射规则
        /// </summary>
        public AutoMapperMappingProfile()
        {
            #region 房间
            CreateMap<Room, RoomDto>().ReverseMap();//房间（加ReverseMap()才支持双向映射）
            CreateMap<RmType, RmTypeDto>().ReverseMap();//房间类型   
            #endregion
            #region 素材管理
            CreateMap<MMAdCampaign, MMAdCampaignDto>().ReverseMap()
                .ForMember(dest => dest.MMPlayEntity, opt => opt.Ignore())
                .ForMember(dest => dest.MMDeviceList, opt => opt.Ignore())
                .ForMember(dest => dest.MMLayoutTemplateEntity, opt => opt.Ignore());//节目投放   
            CreateMap<MMAdCampaign, MMAdCampaignCreateDto>().ReverseMap()
                .ForMember(dest => dest.MMPlayEntity, opt => opt.Ignore())
                .ForMember(dest => dest.MMDeviceList, opt => opt.Ignore())
                .ForMember(dest => dest.MMLayoutTemplateEntity, opt => opt.Ignore());//节目投放 
            CreateMap<MMDevice, MMDeviceDto>().ReverseMap()
                .ForMember(dest => dest.LayoutTemplate, opt => opt.Ignore())
                .ForMember(dest => dest.LayoutRegions, opt => opt.Ignore())
                // .ForMember(dest => dest.OnlineStatus, opt => opt.Ignore())
                .ForMember(dest => dest.finalVisit, opt => opt.MapFrom(src => src.finalVisit));//设备
            CreateMap<MMDevice, MMDeviceCreateDto>().ReverseMap()
                .ForMember(dest => dest.LayoutTemplate, opt => opt.Ignore())
                .ForMember(dest => dest.LayoutRegions, opt => opt.Ignore());
            //.ForMember(dest => dest.OnlineStatus, opt => opt.Ignore());//设备
            //CreateMap<MMDeviceStatus, MMDeviceStatusDto>().ReverseMap();//设备状态    
            CreateMap<MMDeviceFinalVisit, MMDeviceFinalVisitDto>().ReverseMap();//投放最后时间  
            CreateMap<MMFile, MMFileDto>().ReverseMap();//素材   
            CreateMap<MMFile, MMFileCreateDto>().ReverseMap();//素材   
            CreateMap<MMLayoutTemplate, MMLayoutTemplateDto>().ReverseMap();//布局模板   
            CreateMap<MMLayoutTemplate, MMLayoutTemplateCreateDto>().ReverseMap();//布局模板 
            CreateMap<MMLayoutRegion, MMLayoutRegionDto>().ReverseMap()
                // .ForMember(dest => dest.AllowedContentType, opt => opt.MapFrom(src => src.CreatedBy)) // 特殊字段映射
                .ForMember(dest => dest.mMPlaylists, opt => opt.Ignore());
            CreateMap<MMLayoutRegion, MMLayoutRegionCreateDto>().ReverseMap()
    // .ForMember(dest => dest.AllowedContentType, opt => opt.MapFrom(src => src.CreatedBy)) // 特殊字段映射
    .ForMember(dest => dest.mMPlaylists, opt => opt.Ignore());
            CreateMap<MMPlaylist, MMPlaylistDto>().ReverseMap();//节目
            CreateMap<MMPlaylist, MMPlaylistCreateDto>().ReverseMap();//节目
            CreateMap<MMPlaylistDetail, MMPlaylistDetailDto>()
                .ForMember(dest => dest.PlaylistDetailXqList, opt => opt.Ignore());
            CreateMap<MMPlaylistDetail, MMPlaylistDetailCreateDto>()
    .ForMember(dest => dest.PlaylistDetailXqList, opt => opt.Ignore());
            CreateMap<MMPlaylistDetailXq, MMPlaylistDetailXqDto>()
                .ForMember(dest => dest.MMFile, opt => opt.MapFrom(src => src.MMFile));
            CreateMap<MMPlaylistDetailXq, MMPlaylistDetailXqCreateDto>()
    .ForMember(dest => dest.MMFile, opt => opt.MapFrom(src => src.MMFile));

            #endregion
            #region 外部团购
            CreateMap<WayFoodMap, WayFoodMapDto>().ReverseMap();
            CreateMap<WayFoodMap, WayFoodMapCreateDto>().ReverseMap();
            #endregion
            #region 播放壁画管理
            CreateMap<WPTopicITemplate, WPTopicITemplateDto>().ReverseMap();
            CreateMap<WPTopicI, WPTopicIDto>().ReverseMap();
            CreateMap<WPTopicIFile, WPTopicIFileDto>().ReverseMap();

            #endregion
            #region 专员提成
            CreateMap<CommissionAssignShowing, AssignShowingsDto>().ReverseMap();
            CreateMap<CommissionAssignEmployee, AssignEmployeeDto>().ReverseMap();
            CreateMap<CommissionAssignRecommend, AssignRecommendDto>().ReverseMap();

            #endregion
            #region 评价功能
            CreateMap<EvaluationTemplate, EvaluationTemplateDto>().ReverseMap();
            #endregion
        }

    }
}
