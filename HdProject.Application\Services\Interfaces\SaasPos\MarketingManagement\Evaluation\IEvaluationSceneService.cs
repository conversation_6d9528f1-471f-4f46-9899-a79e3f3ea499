﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using HdProject.Domain.DTOs.SaasPos.MarketingManagement.Evaluation.EvaluationScene;

namespace HdProject.Application.Services.Interfaces.SaasPos.MarketingManagement.Evaluation
{
    /// <summary>
    /// 场景接口类
    /// </summary>
    public interface IEvaluationSceneService
    {
        /// <summary>
        /// 查询全部信息
        /// </summary>
        /// <returns></returns>
       Task<EvaluationSceneGetAllResponseDto> GetAllAsync();
    }
}
