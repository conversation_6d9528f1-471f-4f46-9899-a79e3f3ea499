﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using SqlSugar;

namespace HdProject.Domain.Entities.SaasPos.MarketingManagement.Evaluation
{
    /// <summary>
    /// 场景表模型
    /// </summary>
    [SugarTable("MK_Scene")]
    public class Scene
    {
        /// <summary>
        /// 场景ID，自增主键
        /// </summary>
        [SugarColumn(ColumnName = "SceneID", IsPrimaryKey = true, IsIdentity = true)]
        public int SceneId { get; set; }

        /// <summary>
        /// 场景名称
        /// </summary>
        public string SceneName { get; set; }

        /// <summary>
        /// 场景描述
        /// </summary>
        public string? SceneDescription { get; set; }
    }
}
