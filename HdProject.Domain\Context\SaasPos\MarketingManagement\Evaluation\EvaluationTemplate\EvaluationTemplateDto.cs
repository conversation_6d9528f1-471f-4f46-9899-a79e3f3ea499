﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using HdProject.Domain.Context.SaasPos.MarketingManagement.Evaluation.EvaluationContentConfig;
using HdProject.Domain.Context.SaasPos.MarketingManagement.Evaluation.EvaluationIndicator;
using HdProject.Domain.Context.SaasPos.MarketingManagement.Evaluation.EvaluationScene;
using HdProject.Domain.Context.SaasPos.MarketingManagement.Evaluation.TemplateIndicatorRelation;
using SqlSugar;

namespace HdProject.Domain.Context.SaasPos.MarketingManagement.Evaluation.EvaluationTemplate
{
    /// <summary>
    /// 评价模板
    /// </summary>
    public class EvaluationTemplateDto
    {
        /// <summary>
        /// 模板ID
        /// </summary>
        public int TemplateId { get; set; }
        /// <summary>
        /// 模板名称
        /// </summary>
        public string TemplateName { get; set; }

        /// <summary>
        /// 模板描述
        /// </summary>
        public string? TemplateDescription { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime? CreateTime { get; set; }

        /// <summary>
        /// 场景
        /// </summary>
        public List<EvaluationSceneDto> sceneDtos { get; set; }
        /// <summary>
        /// 指标
        /// </summary>
        public List<EvaluationIndicatorDto> indicatorDtos { get; set; }
        /// <summary>
        /// 评价内容配置
        /// </summary>
        public EvaluationContentConfigDto ConfigDto { get; set; }
        ///// <summary>
        ///// 模板关联指标集合
        ///// </summary>
        //public List<TemplateIndicatorRelationDto> indicatorRelationDtos { get; set; }
    }
}
