﻿using SqlSugar;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace HdProject.Domain.Entities.SaasPos
{
    public class StudentVerification
    {
        [SugarColumn(IsPrimaryKey = true,IsIdentity =true)]
        public int Id { get; set; }
        public string OpenId { get; set; }
        public string? Tel { get; set; }
        public string? Sign { get; set; }
        public int UseSummary { get; set; }
        public DateTime LastTime { get; set; }
        public DateTime ValidTime { get; set; }
        public DateTime InTime { get; set; }

    }
}
