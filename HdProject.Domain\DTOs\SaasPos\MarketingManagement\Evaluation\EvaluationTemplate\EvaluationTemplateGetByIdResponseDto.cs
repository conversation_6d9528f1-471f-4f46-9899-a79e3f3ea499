﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using HdProject.Domain.Context.SaasPos.MarketingManagement.Evaluation.EvaluationIndicator;
using HdProject.Domain.Context.SaasPos.MarketingManagement.Evaluation.EvaluationTemplate;

namespace HdProject.Domain.DTOs.SaasPos.MarketingManagement.Evaluation.EvaluationTemplate
{
    /// <summary>
    /// 根据ID查询评价模板信息的响应类
    /// </summary>
    public class EvaluationTemplateGetByIdResponseDto
    {
        public EvaluationTemplateDto Model { get; set; }
        
    }
}
