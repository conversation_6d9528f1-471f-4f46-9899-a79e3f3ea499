﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using SqlSugar;

namespace HdProject.Domain.Entities.SaasPos.MarketingManagement.Evaluation
{
    /// <summary>
    /// 评价模板表模型
    /// </summary>
    [SugarTable("MK_EvaluationTemplate")]
    public class EvaluationTemplate
    {
        /// <summary>
        /// 模板ID，自增主键
        /// </summary>
        [SugarColumn(ColumnName = "TemplateID", IsPrimaryKey = true, IsIdentity = true)]
        public int TemplateId { get; set; }
        /// <summary>
        /// 模板名称
        /// </summary>
        public string TemplateName { get; set; }

        /// <summary>
        /// 模板描述
        /// </summary>
        public string? TemplateDescription { get; set; }

        /// <summary>
        /// 禁用状态
        /// </summary>
        public bool IsDisable { get; set; }

        /// <summary>
        /// 禁用人
        /// </summary>
        public string? DisabledBy { get; set; }

        /// <summary>
        /// 禁用时间
        /// </summary>
        public DateTime? DisabledTime { get; set; }

        /// <summary>
        /// 创建人
        /// </summary>
        public string? CreateBy { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime? CreateTime { get; set; }

        /// <summary>
        /// 修改人
        /// </summary>
        public string? ModifiedBy { get; set; }

        /// <summary>
        /// 修改时间
        /// </summary>
        public DateTime? ModifiedTime { get; set; }

    }

}
