﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using SqlSugar;

namespace HdProject.Domain.Entities.SaasPos.MarketingManagement.Evaluation
{
    /// <summary>
    /// 模板-指标关联表模型
    /// </summary>
    [SugarTable("MK_TemplateIndicatorRelation")]
    public class TemplateIndicatorRelation
    {
        /// <summary>
        /// 关联关系ID，自增主键
        /// </summary>
        [SugarColumn(ColumnName = "RelationID", IsPrimaryKey = true, IsIdentity = true)]
        public int RelationId { get; set; }
        /// <summary>
        /// 外键，关联评价模板表的TemplateID
        /// </summary>
        public int TemplateId { get; set; }

        /// <summary>
        /// 外键，关联评价指标表的IndicatorID
        /// </summary>
        public int IndicatorId { get; set; }

        /// <summary>
        /// 指标在模板中的显示顺序
        /// </summary>
        public int SortOrder { get; set; }

    }

}
