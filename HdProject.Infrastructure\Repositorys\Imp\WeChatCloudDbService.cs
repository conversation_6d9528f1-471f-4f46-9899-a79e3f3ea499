﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http.Headers;
using System.Net.Http.Json;
using System.Security.Policy;
using System.Text;
using System.Threading.Tasks;
using Azure.Core;
using HdProject.Domain.Context;
using HdProject.Domain.Context.WeChat;
using HdProject.Domain.Interfaces;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.StaticFiles;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using static HdProject.Domain.Context.MarketingActivityManagement.MarketingActivityContext;
//using WeChatApiException = HdProject.Domain.Context.MarketingActivityManagement.MarketingActivityContext.WeChatApiException;

namespace HdProject.Infrastructure.Repositorys.Imp
{
    /// <summary>
    /// 操作微信小程序的云开发数据库
    /// </summary>
    public class WeChatCloudDbService : IWeChatCloudDbService
    {
        private readonly HttpClient _httpClient;
        private readonly string _appId;
        private readonly string _appSecret;
        private readonly string _envId;
        private string _cachedToken;
        private DateTime _tokenExpireTime;
        private readonly object _tokenLock = new();

        public WeChatCloudDbService(
            HttpClient httpClient,
            string appId,
            string appSecret,
            string envId)
        {
            _httpClient = httpClient;
            _appId = appId;
            _appSecret = appSecret;
            _envId = envId;
            //_mchId = mchId;商户号
        }

        public async Task<string> GetAccessTokenAsync()
        {
            // 检查缓存有效性
            if (!string.IsNullOrEmpty(_cachedToken) &&
                DateTime.UtcNow < _tokenExpireTime)
            {
                return _cachedToken;
            }

            lock (_tokenLock)
            {
                // 双重检查
                if (!string.IsNullOrEmpty(_cachedToken) &&
                    DateTime.UtcNow < _tokenExpireTime)
                {
                    return _cachedToken;
                }

                // 使用微信官方接口获取token
                //var url = $"cgi-bin/token?grant_type=client_credential&appid={_appId}&secret={_appSecret}";
                var url = "http://r.tang-hui.com.cn/api/public_db.ashx?db=15&key=th_miniapp_seasonscard_token";
                var response = _httpClient.GetAsync(url).GetAwaiter().GetResult();
                response.EnsureSuccessStatusCode();

                var content = response.Content.ReadAsStringAsync().Result;
                var tokenResponse = JsonConvert.DeserializeObject<TokenResponse>(content);

                if (tokenResponse == null || string.IsNullOrEmpty(tokenResponse.access_token))
                {
                    throw new Exception("获取 access_token 失败，响应内容无效");
                }

                _cachedToken = tokenResponse.access_token;
                _tokenExpireTime = DateTime.UtcNow.AddSeconds(tokenResponse.expires_in - 300);
            }

            return _cachedToken;
        }
        /// <summary>
        /// 查询
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        public async Task<string> QueryDatabaseAsync(string query)
        {
            var accessToken = await GetAccessTokenAsync();
            var url = $"https://api.weixin.qq.com/tcb/databasequery?access_token={accessToken}";

            var requestBody = new JObject
            {
                { "env", _envId },
                { "query", query }
            };


            var response = await _httpClient.PostAsync(url,
                new StringContent(requestBody.ToString(), Encoding.UTF8, "application/json"));

            response.EnsureSuccessStatusCode();
            return await response.Content.ReadAsStringAsync();
        }
        /// <summary>
        /// 修改
        /// </summary>
        /// <param name="command"></param>
        /// <returns></returns>
        public async Task<string> ExecuteDatabaseCommandAsync(string command)
        {
            var accessToken = await GetAccessTokenAsync();
            var url = $"https://api.weixin.qq.com/tcb/databaseupdate?access_token={accessToken}";

            // 使用JObject构建请求体（与QueryDatabaseAsync保持一致）
            var requestBody = new JObject
            {
             { "env", _envId },
             { "query", command }
            };

            // 使用相同的HTTP请求方式
            var response = await _httpClient.PostAsync(
                url,
                new StringContent(requestBody.ToString(), Encoding.UTF8, "application/json")
            );

            response.EnsureSuccessStatusCode();
            return await response.Content.ReadAsStringAsync();
        }
        /// <summary>
        /// 新增
        /// </summary>
        /// <param name="command"></param>
        /// <returns></returns>
        /// <exception cref="NotImplementedException"></exception>
        public async Task<string> AddeDatabaseCommandAsync(string command)
        {
            var accessToken = await GetAccessTokenAsync();
            var url = $"https://api.weixin.qq.com/tcb/databaseadd?access_token={accessToken}";

            // 使用JObject构建请求体（与QueryDatabaseAsync保持一致）
            var requestBody = new JObject
            {
             { "env", _envId },
             { "query", command }
            };

            // 使用相同的HTTP请求方式
            var response = await _httpClient.PostAsync(
                url,
                new StringContent(requestBody.ToString(), Encoding.UTF8, "application/json")
            );

            response.EnsureSuccessStatusCode();
            return await response.Content.ReadAsStringAsync();
        }
        /// <summary>
        /// 文件上传链接(第一步：获取上传凭证)
        /// </summary>
        /// <param name = "file" > 上传的文件 </ param >
        /// < param name="fileName">文件名</param>
        /// <returns>云存储文件ID</returns>
        public async Task<string> UploadImageToCloudStorage(IFormFile file, string fileName)
        {
            var accessToken = await GetAccessTokenAsync();
            var url = $"https://api.weixin.qq.com/tcb/uploadfile?access_token={accessToken}";

            // 明确指定路径格式
            var path = $"images/{fileName.TrimStart('/')}"; // 防止路径以/开头

            var requestBody = new JObject
    {
        { "env", _envId },
        { "path", path }
    };

            var response = await _httpClient.PostAsync(
                url,
                new StringContent(requestBody.ToString(), Encoding.UTF8, "application/json")
            );

            if (!response.IsSuccessStatusCode)
            {
                var errorContent = await response.Content.ReadAsStringAsync();
                throw new HttpRequestException($"获取上传凭证HTTP错误: {response.StatusCode}, {errorContent}");
            }

            var responseJson = await response.Content.ReadAsStringAsync();
            var responseObj = JObject.Parse(responseJson);

            // 检查微信API错误码
            if (responseObj["errcode"]?.Value<int>() != 0)
            {
                throw new Exception($"微信API错误: {responseObj["errmsg"]}");
            }

            // 确保包含必要字段
            if (new[] { "url", "authorization", "token", "cos_file_id" }
                .Any(field => !responseObj.ContainsKey(field)))
            {
                throw new Exception("响应缺少必要字段");
            }

            // 添加path到响应（供后续使用）
            responseObj["path"] = path;
            return responseObj.ToString();
        }

        /// <summary>
        /// 执行文件上传到云存储的第二步（实际文件内容上传）
        /// </summary>
        /// <param name="file">要上传的文件对象</param>
        /// <param name="uploadResponseJson">第一步获取的上传凭证JSON响应</param>
        /// <returns>云端文件ID</returns>
        public async Task<int> CompleteCloudStorageUpload(IFormFile file, string uploadResponseJson)
        {
            // 1. 解析并验证响应数据
            var responseObj = JObject.Parse(uploadResponseJson);
            var uploadUrl = responseObj["url"]?.ToString();
            var authorization = responseObj["authorization"]?.ToString();
            var token = responseObj["token"]?.ToString();
            var cosFileId = responseObj["cos_file_id"]?.ToString();
            var filePath = responseObj["path"]?.ToString();

            if (new[] { uploadUrl, authorization, token, cosFileId, filePath }.Any(string.IsNullOrWhiteSpace))
            {
                throw new ArgumentException("上传凭证缺少必要参数");
            }

            // 2. 创建边界并手动控制格式
            var boundary = $"----WebKitFormBoundary{Guid.NewGuid():N}";
            using var content = new MultipartFormDataContent(boundary);

            // 3. 强制设置正确的Content-Type头（关键修复）
            content.Headers.Remove("Content-Type");
            content.Headers.TryAddWithoutValidation(
                "Content-Type",
                $"multipart/form-data; boundary={boundary}"
            );

            // 4. 严格按照顺序添加字段（确保格式正确）
            AddFormField(content, "key", filePath!);
            AddFormField(content, "Signature", authorization!);
            AddFormField(content, "x-cos-security-token", token!);
            AddFormField(content, "x-cos-meta-fileid", cosFileId!);

            // 5. 添加文件（特殊处理文件名编码）
            using var fileStream = file.OpenReadStream();
            var fileContent = new StreamContent(fileStream)
            {
                Headers =
        {
            ContentType = new MediaTypeHeaderValue(GetMimeType(file.FileName)),
            ContentDisposition = new ContentDispositionHeaderValue("form-data")
            {
                Name = "\"file\"",
                FileName = $"\"{Path.GetFileName(file.FileName)}\""
            }
        }
            };
            content.Add(fileContent);

            // 6. 配置HttpClient（增加重试和超时设置）
            using var httpClient = new HttpClient(new HttpClientHandler
            {
                AllowAutoRedirect = false
            })
            {
                Timeout = TimeSpan.FromSeconds(30)
            };

            // 7. 添加必要的请求头
            httpClient.DefaultRequestHeaders.Add("Host", new Uri(uploadUrl!).Host);
            httpClient.DefaultRequestHeaders.TryAddWithoutValidation("User-Agent", "COS-Upload");

            // 8. 发送请求并处理响应
            try
            {

                var response = await httpClient.PostAsync(uploadUrl, content);
                var responseContent = await response.Content.ReadAsStringAsync();

                if (!response.IsSuccessStatusCode)
                {
                    throw new Exception($"COS上传失败 (HTTP {response.StatusCode}): {responseContent}");
                }

                return (int)response.StatusCode;
            }
            catch (TaskCanceledException)
            {
                throw new Exception("请求超时，请检查网络或增大超时时间");
            }
            catch (Exception ex)
            {
                throw new Exception($"上传请求失败: {ex.Message}");
            }
        }

        // 辅助方法：标准化表单字段添加
        private void AddFormField(MultipartFormDataContent content, string name, string value)
        {
            var fieldContent = new StringContent(value);
            fieldContent.Headers.ContentDisposition = new ContentDispositionHeaderValue("form-data")
            {
                Name = $"\"{name}\""
            };
            content.Add(fieldContent);
        }

        private static string GetMimeType(string fileName)
        {
            var provider = new FileExtensionContentTypeProvider();
            return provider.TryGetContentType(fileName, out var mimeType)
                ? mimeType
                : "application/octet-stream";
        }

        /// <summary>
        /// 文件下载链接
        /// </summary>
        /// <param name="file"></param>
        /// <param name="fileName"></param>
        /// <returns></returns>
        public async Task<string> DownloadImageToCloudStorage(string fileId)
        {
            var accessToken = await GetAccessTokenAsync();
            var url = $"https://api.weixin.qq.com/tcb/batchdownloadfile?access_token={accessToken}";

            var requestBody = new JObject
            {
                ["env"] = _envId,
                ["file_list"] = new JArray
        {
            new JObject
            {
                ["fileid"] = fileId,
                ["max_age"] = 7200
            }
        }
            };

            var response = await _httpClient.PostAsync(
                url,
                new StringContent(requestBody.ToString(), Encoding.UTF8, "application/json")
            );
            response.EnsureSuccessStatusCode();

            var responseStr = await response.Content.ReadAsStringAsync();
            var responseJson = JObject.Parse(responseStr);

            // 检查文件是否存在
            if (responseJson["file_list"]?[0]?["status"]?.Value<int>() == 0)
            {
                return responseJson["file_list"]?[0]?["download_url"]?.ToString();
            }
            else
            {
                throw new Exception($"文件不存在: {responseJson["file_list"]?[0]?["errmsg"]}");
            }
        }

        /// <summary>
        /// 生成无限制的小程序码
        /// </summary>
        /// <param name="pagePath">小程序页面路径</param>
        /// <param name="scene">场景值</param>
        /// <returns>返回图片的Base64字符串</returns>
        public async Task<byte[]> GenerateMiniProgramQrCodeAsync(string fullPath)
        {
            // 分离page和scene
            var parts = fullPath.Split('?');
            var page = parts[0];
            var scene = parts.Length > 1 ? parts[1].Replace("scene=", "") : "";

            var accessToken = await GetAccessTokenAsync();
            var url = $"https://api.weixin.qq.com/wxa/getwxacodeunlimit?access_token={accessToken}";

            var requestData = new
            {
                page = page,
                scene = scene,
                width = 430
            };
            //将请求数据对象序列化为JSON字符串
            var json = JsonConvert.SerializeObject(requestData);
            //创建HTTP请求内容（设置编码和内容类型）
            var content = new StringContent(json, Encoding.UTF8, "application/json");
            //发送POST请求
            var response = await _httpClient.PostAsync(url, content);
            //读取响应内容为字节数组
            return await response.Content.ReadAsByteArrayAsync();
        }
        /// <summary>
        /// 申请退款
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        public async Task<string> ApplyRefund(RefundParams query)
        {
            var accessToken = await GetAccessTokenAsync();
            var url = $"https://api.weixin.qq.com/shop/pay/refundorder?access_token={accessToken}";



            var requestBody = new JObject
    {
        { "openid", query.openid },
        { "mchid",""},//商户号
        { "trade_no", ""},//可不传参数
        { "transaction_id", query.transaction_id },
        { "refund_no", query.refund_no},
        { "total_amount", query.total_amount },
        { "refund_amount", query.refund_amount}
    };

            var response = await _httpClient.PostAsync(url,
                new StringContent(requestBody.ToString(), Encoding.UTF8, "application/json"));

            response.EnsureSuccessStatusCode();
            return await response.Content.ReadAsStringAsync();
        }

        public async Task<string> invokeCloudFunction(string functionName, object data)
        {
            var accessToken = await GetAccessTokenAsync();
          
            var url = $"https://api.weixin.qq.com/tcb/invokecloudfunction?access_token={accessToken}&env={_envId}&name={functionName}";

            // 序列化请求体
            var requestBody = JsonConvert.SerializeObject(data);

            var response = await _httpClient.PostAsync(
                url,
                new StringContent(requestBody, Encoding.UTF8, "application/json"));

            response.EnsureSuccessStatusCode();
            return await response.Content.ReadAsStringAsync();
        }
        /// <summary>
        /// 微信大学生认证
        /// </summary>
        /// <param name="openid"></param>
        /// <param name="wxStudentCheckCode"></param>
        /// <returns></returns>
        /// <exception cref="HttpRequestException"></exception>
        /// <exception cref="WeChatApiException"></exception>
        public async Task<string> QuickCheckStudentIdentity(string openId, string wxStudentCheckCode)
        {
            // 1. 获取访问令牌
            var accessToken = await GetAccessTokenAsync();

            // 2. 构建完整API地址
            var apiUrl = $"https://api.weixin.qq.com/intp/quickcheckstudentidentity?access_token={accessToken}";

            // 3. 构建符合微信规范的请求体
            var requestBody = new JObject
            {
                ["openid"] = openId,
                ["wx_studentcheck_code"] = wxStudentCheckCode
            };

            // 4. 发送HTTP请求
            using var content = new StringContent(
                requestBody.ToString(),
                Encoding.UTF8,
                "application/json"
            );

            var response = await _httpClient.PostAsync(apiUrl, content);

            // 5. 处理响应
            var responseJson = await response.Content.ReadAsStringAsync();

            // 6. 状态码检查（网络层）
            if (!response.IsSuccessStatusCode)
            {
                throw new HttpRequestException($"HTTP请求失败 [{(int)response.StatusCode}] {responseJson}");
            }

            // 7. 业务错误检查（微信API层）
            var responseObj = JObject.Parse(responseJson);
            if (responseObj["errcode"]?.Value<int>() != 0)
            {
                throw new WeChatApiException(
                    responseObj["errcode"]?.Value<int>() ?? -1,
                    responseObj["errmsg"]?.Value<string>() ?? "未知错误"
                );
            }

            // 8. 返回原始响应数据
            return responseJson;
        }
    }
}
