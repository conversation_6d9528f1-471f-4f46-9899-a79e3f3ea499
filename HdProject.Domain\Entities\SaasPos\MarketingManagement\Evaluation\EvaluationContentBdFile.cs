﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using SqlSugar;

namespace HdProject.Domain.Entities.SaasPos.MarketingManagement.Evaluation
{
    /// <summary>
    /// // 评价内容关联素材表实体类
    /// </summary>
    [SugarTable("MK_EvaluationContentBdFile")]
    public class EvaluationContentBdFile
    {
        /// <summary>
        /// 自增主键
        /// </summary>
        [SugarColumn(IsPrimaryKey = true, IsIdentity = true)]
        public int CBFID { get; set; }
        /// <summary>
        /// 内容ID
        /// </summary>
        public int ContentID { get; set; }

        /// <summary>
        /// 文件ID
        /// </summary>
        public int FileID { get; set; }

    }
}
