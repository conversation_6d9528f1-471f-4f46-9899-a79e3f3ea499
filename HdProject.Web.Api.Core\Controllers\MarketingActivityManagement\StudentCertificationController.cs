﻿using HdProject.Application.Services.Interfaces.MarketingActivityManagement;
using HdProject.Application.Services.Interfaces.Rms;
using HdProject.Domain.Context.MarketingActivityManagement;
using HdProject.Domain.Context.RMS.SummaryStoreTimeSlotDailyList;
using HdProject.Web.Core;
using Microsoft.AspNetCore.Mvc;
using static HdProject.Domain.Context.MarketingActivityManagement.StudentCertificationContext;

namespace HdProject.Web.Api.Core.Controllers.MarketingActivityManagement
{
    /// <summary>
    /// 大学生认证
    /// </summary>
    [Route("[controller]/[Action]")]
    public class StudentCertificationController : PublicControllerBase
    {
        private readonly IStudentCertificationService _studentCertificationService;

        public StudentCertificationController(IStudentCertificationService studentCertificationService)
            => _studentCertificationService = studentCertificationService;

        /// <summary>
        /// 微信大学生认证
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<IActionResult> WxStudentAuthenticationRecord(WxStudentAuthenticationContext context)
        {
            var res = await _studentCertificationService.WxStudentAuthenticationRecord(context);
            return ApiData(res);

        }
        /// <summary>
        /// 本地学生认证
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        [HttpGet]
        public async Task<IActionResult> LocalStudentAuthenticationRecord([FromQuery] LocalStudentAuthenticationContext context)
        {
            var res = await _studentCertificationService.LocalStudentAuthenticationRecord(context);
            return ApiData(res);

        }
        /// <summary>
        /// 学生消费记录
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<IActionResult> StudentConsumptionRecord(StudentConsumptionContext context)
        {
            var res = await _studentCertificationService.StudentConsumptionRecord(context);
            return ApiData(res);

        }
    }
}
