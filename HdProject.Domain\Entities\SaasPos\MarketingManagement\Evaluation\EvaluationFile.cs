﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using SqlSugar;

namespace HdProject.Domain.Entities.SaasPos.MarketingManagement.Evaluation
{
    /// <summary>
    /// 评价素材表
    /// </summary>
    /// 
    [SugarTable("MK_EvaluationFile")]
    public class EvaluationFile
    {
        /// <summary>
        /// 自增主键
        /// </summary>
        [SugarColumn(IsPrimaryKey = true, IsIdentity = true)]
        public int FileID { get; set; }
        /// <summary>
        /// 文件名称
        /// </summary>
        public string FileName { get; set; }

        /// <summary>
        /// 文件路径
        /// </summary>
        public string FilePath { get; set; }

        /// <summary>
        /// 缩略图路径
        /// </summary>
        public string? ThumbnailPath { get; set; }

        /// <summary>
        /// 文件格式类型
        /// </summary>
        public string? FormatType { get; set; }

        /// <summary>
        /// 文件大小
        /// </summary>
        public long? FileSize { get; set; }

        /// <summary>
        /// 上传人
        /// </summary>
        [SugarColumn(Length = 255)]
        public string? UploadedBy { get; set; }

        /// <summary>
        /// 上传时间
        /// </summary>
        public DateTime? UploadedTime { get; set; }

        /// <summary>
        /// 宽度
        /// </summary>
        public int? Width { get; set; }

        /// <summary>
        /// 高度
        /// </summary>
        public int? Height { get; set; }

        /// <summary>
        /// 是否有效
        /// </summary>
        public bool IsActive { get; set; }

    }
}
