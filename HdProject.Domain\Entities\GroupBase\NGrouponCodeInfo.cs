﻿using SqlSugar;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace HdProject.Domain.Entities.GroupBase
{
    public class NGrouponCodeInfo
    {
        [SugarColumn(IsPrimaryKey = true)]
        public Guid CodeKey { get; set; } = Guid.NewGuid();
        public Guid GrouponKey { get; set; } = Guid.NewGuid();
        public string Code { get; set; }
        public int CodeStatus { get; set; } = 0;
        public string Openid { get; set; }
        public DateTime CreateDatetime { get; set; } = DateTime.Now;
        public int? ConsumeShopId { get; set; }
        public DateTime? ConsumeDatetime { get; set; }
        public string ConsumeName { get; set; }
        public string UseRmNo { get; set; }
        public bool isDel { get; set; } = false;
        public string DelMsg { get; set; }
        public DateTime? DelDateTime { get; set; }
        public DateTime? ReceiveDateTime { get; set; }
        public Guid? Batchnumber { get; set; }
        public string transaction_id { get; set; }
        public string UID { get; set; }
        public Guid? DistributeId { get; set; }
        public string InvNo { get; set; }
       
    }
}
