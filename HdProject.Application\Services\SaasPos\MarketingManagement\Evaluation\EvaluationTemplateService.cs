﻿using System;
using System.Collections.Generic;
using System.DirectoryServices.Protocols;
using System.Linq;
using System.Linq.Expressions;
using System.Text;
using System.Threading.Tasks;
using AutoMapper;
using HdProject.Application.Services.Interfaces.SaasPos.MarketingManagement.Evaluation;
using HdProject.Domain.Context.SaasPos.MarketingManagement.Evaluation.EvaluationContentConfig;
using HdProject.Domain.Context.SaasPos.MarketingManagement.Evaluation.EvaluationIndicator;
using HdProject.Domain.Context.SaasPos.MarketingManagement.Evaluation.EvaluationIndicatorDetails;
using HdProject.Domain.Context.SaasPos.MarketingManagement.Evaluation.EvaluationScene;
using HdProject.Domain.Context.SaasPos.MarketingManagement.Evaluation.EvaluationTemplate;
using HdProject.Domain.Context.SaasPos.MarketingManagement.Evaluation.TemplateIndicatorRelation;
using HdProject.Domain.DTOs.SaasPos.MarketingManagement.Evaluation.EvaluationTemplate;
using HdProject.Domain.Entities.SaasPos.MarketingManagement.Evaluation;
using HdProject.Domain.Interfaces;
using HdProject.Domain.Result.Page;
using LinqKit;
using Microsoft.Extensions.Configuration;
using SqlSugar;

namespace HdProject.Application.Services.SaasPos.MarketingManagement.Evaluation
{
    public class EvaluationTemplateService : IEvaluationTemplateService
    {
        private readonly IRepositorySaas<EvaluationTemplate> _repositorySaasEt;//评价模板
        private readonly IRepositorySaas<EvaluationContentConfig> _repositorySaasEcc;//评价内容配置
        private readonly ISqlSugarClient _sqlSugarClient;
        private readonly IMapper _mapper;
        public EvaluationTemplateService(IRepositorySaas<EvaluationTemplate> repositorySaasEt, IRepositorySaas<EvaluationContentConfig> repositorySaasEcc, ISqlSugarClient sqlSugarClient, IMapper mapper)
        {
            _repositorySaasEt = repositorySaasEt;
            _repositorySaasEcc = repositorySaasEcc;
            _sqlSugarClient = sqlSugarClient;
            _mapper = mapper;
        }
        private ISqlSugarClient _db
        {
            get
            {
                return _sqlSugarClient.AsTenant().GetConnection("Saas");
            }
        }
        /// <summary>
        /// 新增
        /// </summary>
        /// <param name="requestDto"></param>
        /// <returns></returns>
        /// <exception cref="NotImplementedException"></exception>
        public async Task<EvaluationTemplateAddResponseDto> AddAsync(EvaluationTemplateAddRequestDto requestDto)
        {
            EvaluationTemplateAddResponseDto responseDto = new EvaluationTemplateAddResponseDto();
            try
            {
                var result = await _db.Ado.UseTranAsync(async () =>
                {
                    var Teplatemodel = new EvaluationTemplate()
                    {
                        TemplateName = requestDto.Model.TemplateName,
                        TemplateDescription = requestDto.Model.TemplateDescription,
                        CreateTime = DateTime.Now
                    };
                    var tpID = await _db.Insertable(Teplatemodel).ExecuteReturnIdentityAsync();//首先新增评价模板，并返回其自增列ID

                    var contentConfig = new EvaluationContentConfig()
                    {
                        TemplateID = tpID,
                        IsTextEvaluation = requestDto.ConfigCreateDto.IsTextEvaluation,
                        IsImageEvaluation = requestDto.ConfigCreateDto.IsImageEvaluation,
                        IsVideoEvaluation = requestDto.ConfigCreateDto.IsVideoEvaluation,
                        IsRequired = requestDto.ConfigCreateDto.IsRequired,
                        GuideText = requestDto.ConfigCreateDto.GuideText,
                    };
                    await _db.Insertable(contentConfig).ExecuteCommandAsync();//新增评价内容配置

                    var sceneTemplates = new List<SceneTemplateRelation>();
                    foreach (var sceneCreateDto in requestDto.sceneCreateDtos)
                    {
                        var sceneTemplate = new SceneTemplateRelation()
                        {
                            SceneId = sceneCreateDto.SceneId,
                            TemplateId = tpID
                        };
                        sceneTemplates.Add(sceneTemplate);
                    }
                    await _db.Insertable(sceneTemplates).ExecuteCommandAsync();//新增模板场景关系表

                    //var IndicatorRelationList = new List<TemplateIndicatorRelation>();
                    int so = 1;
                    foreach (var item in requestDto.IndicatorCreateDto)
                    {
                        var IndicatorModel = new EvaluationIndicator()
                        {
                            IndicatorName = item.IndicatorName,
                            IndicatorType = item.IndicatorType,
                            IndicatorDescription = item.IndicatorDescription,
                            IsRequired = item.IsRequired
                        };
                        var eid = await _db.Insertable(IndicatorModel).ExecuteReturnIdentityAsync();//新增模板指标项，返回自增ID
                        var indicatorRelationModel = new TemplateIndicatorRelation()
                        {
                            TemplateId = tpID,
                            IndicatorId = eid,
                            SortOrder = so
                        };
                        // IndicatorRelationList.Add(indicatorRelationModel);
                        var Inid = await _db.Insertable(indicatorRelationModel).ExecuteReturnIdentityAsync();//新增模板指标关系表
                        if (item.detailsCreateDtos.Count > 0)
                        {
                            var indicatorDetails = new List<EvaluationIndicatorDetails>();
                            foreach (var details in item.detailsCreateDtos)
                            {
                                var detailsModel = new EvaluationIndicatorDetails()
                                {
                                    IndicatorID = Inid,
                                    DetailsIDName = details.DetailsIDName,
                                };
                                indicatorDetails.Add(detailsModel);
                            }
                            await _db.Insertable(indicatorDetails).ExecuteCommandAsync();//新增指标明细表
                        }
                        so++;
                    }

                });

                if (result.IsSuccess)
                {
                    responseDto.Index = 1;
                }
                else
                {
                    responseDto.Index = 0;
                }
            }
            catch (Exception ex)
            {

                throw new Exception("新增失败：" + ex.Message);
            }
            return responseDto;
        }
        /// <summary>
        /// 删除
        /// </summary>
        /// <param name="requestDto"></param>
        /// <returns></returns>
        public async Task<EvaluationTemplateDeleteResponseDto> DeleteAsync(EvaluationTemplateDeleteRequestDto requestDto)
        {
            EvaluationTemplateDeleteResponseDto responseDto = new EvaluationTemplateDeleteResponseDto();
            try
            {
                var resultModel = await _repositorySaasEt.GetFirstAsync(a => a.TemplateId == requestDto.TemplateId && !a.IsDisable);
                if (resultModel == null)
                {
                    throw new Exception("无法删除，请检查该评价模板是否存在！");
                }
                resultModel.IsDisable = true;
                resultModel.DisabledTime = DateTime.Now;
                var result = await _repositorySaasEt.UpdateAsync(resultModel);
                responseDto.Index = result;
            }
            catch (Exception ex)
            {
                throw new Exception("删除失败：" + ex.Message);
            }
            return responseDto;
        }

        /// <summary>
        /// 查询
        /// </summary>
        /// <param name="requestDto"></param>
        /// <returns></returns>
        /// <exception cref="NotImplementedException"></exception>
        public async Task<EvaluationTemplateGetAllResponseDto> GetAllAsync(EvaluationTemplateGetAllRequestDto requestDto)
        {
            EvaluationTemplateGetAllResponseDto responseDto = new EvaluationTemplateGetAllResponseDto();
            var predicate = PredicateBuilder.New<EvaluationTemplate>(true);
            predicate = predicate.And(it => it.IsDisable == false);//过滤非禁用的数据
                                                                   // 动态添加条件
            if (requestDto.QueryCriteria != null && requestDto.QueryCriteria.Trim() != "")
            {
                predicate = predicate.And(it => it.TemplateName.Contains(requestDto.QueryCriteria));
            }
            var result = await _repositorySaasEt.GetPageListAsync(requestDto, predicate);
            var model = _mapper.Map<List<EvaluationTemplateDto>>(result);//使用AutoMapper进行对象属性映射
            foreach (var m in model)
            {
                var scenes = await _db.Queryable<SceneTemplateRelation>()
                .Where(c => c.TemplateId == m.TemplateId).Select(a => new SceneTemplateRelation
                {
                    LinkId = a.LinkId,
                    SceneId = a.SceneId,
                    TemplateId = a.TemplateId,
                }).ToListAsync();//查询场景关系表
                var sceneDto = new List<EvaluationSceneDto>();
                foreach (var item in scenes)
                {
                    var evaluationModel = await _db.Queryable<Scene>()
                     .Where(b => b.SceneId == item.SceneId).Select(i => new EvaluationSceneDto
                     {
                         SceneId = i.SceneId,
                         SceneName = i.SceneName,
                         SceneDescription = i.SceneDescription,
                     }).FirstAsync();//查询场景信息
                    sceneDto.Add(evaluationModel);
                }
                m.sceneDtos = sceneDto;
            }
            responseDto.Model = model;
            return responseDto;
        }

        //public async Task<List<EvaluationTemplateDto>> GetPageAllAssociationBdAsync(Pagination page, Expression<Func<EvaluationTemplate, bool>> whereExpression = null)
        //{
        //    RefAsync<int> totalCount = 0;
        //    var query = _db.Queryable<EvaluationTemplate>().OrderByDescending(st => st.CreateTime);

        //    if (whereExpression != null)
        //    {
        //        query = query.Where(whereExpression);
        //    }

        //    var list = await query.ToPageListAsync(page.Page, page.Rows, totalCount);
        //    page.Records = totalCount;
        //    return list;

        //}

        /// <summary>
        /// 根据ID查询
        /// </summary>
        /// <param name="requestDto"></param>
        /// <returns></returns>
        /// <exception cref="NotImplementedException"></exception>
        public async Task<EvaluationTemplateGetByIdResponseDto> GetByIdAsync(EvaluationTemplateGetByIdRequestDto requestDto)
        {
            EvaluationTemplateGetByIdResponseDto responseDto = new EvaluationTemplateGetByIdResponseDto();
            try
            {
                var result = await GetAssociationBdDetailsAsync(requestDto.TemplateId);
                if (result != null)
                {
                    responseDto.Model = result;
                }
            }
            catch (Exception ex)
            {
                throw new Exception("操作失败：" + ex.Message);
            }
            return responseDto;
        }



        /// <summary>
        /// 根据ID查询，需要关联其他实体信息
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public async Task<EvaluationTemplateDto> GetAssociationBdDetailsAsync(int id)
        {
            var template = await _db.Queryable<EvaluationTemplate>()
            .Where(c => c.TemplateId == id && c.IsDisable == false)
            .FirstAsync();//查询评价模板信息
            if (template == null)
            {
                return null;
            }

            var scenes = await _db.Queryable<SceneTemplateRelation>()
            .Where(c => c.TemplateId == template.TemplateId).Select(a => new SceneTemplateRelation
            {
                LinkId = a.LinkId,
                SceneId = a.SceneId,
                TemplateId = a.TemplateId,
            }).ToListAsync();//查询场景关系表
            var configModel = await _db.Queryable<EvaluationContentConfig>()
            .Where(c => c.TemplateID == template.TemplateId).Select(i => new EvaluationContentConfigDto
            {
                ContentConfigID = i.ContentConfigID,
                TemplateID = i.TemplateID,
                IsTextEvaluation = i.IsTextEvaluation,
                IsImageEvaluation = i.IsImageEvaluation,
                IsVideoEvaluation = i.IsVideoEvaluation,
                IsRequired = i.IsRequired,
                GuideText = i.GuideText,
            }).FirstAsync();//查询评价内容配置表
            List<EvaluationSceneDto> evaluationSceneDtos = new List<EvaluationSceneDto>();
            foreach (var item in scenes)
            {
                var evaluationModel = await _db.Queryable<Scene>()
                 .Where(b => b.SceneId == item.SceneId).Select(i => new EvaluationSceneDto
                 {
                     SceneId = i.SceneId,
                     SceneName = i.SceneName,
                     SceneDescription = i.SceneDescription,
                 }).FirstAsync();//查询场景信息
                evaluationSceneDtos.Add(evaluationModel);
            }

            var tirModel = await _db.Queryable<TemplateIndicatorRelation>()
            .Where(c => c.TemplateId == template.TemplateId).Select(a => new TemplateIndicatorRelationDto
            {
                RelationId = a.RelationId,
                TemplateId = a.TemplateId,
                IndicatorId = a.IndicatorId,
                SortOrder = a.SortOrder
            }).ToListAsync();//查询关系表

            List<EvaluationIndicatorDto> evaluationIndicators = new List<EvaluationIndicatorDto>();
            foreach (var tir in tirModel)
            {
                var evaluationModel = await _db.Queryable<EvaluationIndicator>()
                .Where(c => c.IndicatorId == tir.IndicatorId).Select(i => new EvaluationIndicatorDto
                {
                    IndicatorId = i.IndicatorId,
                    IndicatorName = i.IndicatorName,
                    IndicatorType = i.IndicatorType,
                    IndicatorDescription = i.IndicatorDescription,
                    IsRequired = i.IsRequired,
                    SortOrder = tir.SortOrder
                }).FirstAsync();//查询指标信息
                var IndicatorDetails = await _db.Queryable<EvaluationIndicatorDetails>()
                .Where(c => c.IndicatorID == tir.IndicatorId).Select(i => new EvaluationIndicatorDetailsDto
                {
                    DetailsID = i.DetailsID,
                    IndicatorID = i.IndicatorID,
                    DetailsIDName = i.DetailsIDName,
                }).ToListAsync();//查询指标明细信息
                evaluationModel.detailsDtos = IndicatorDetails;
                evaluationIndicators.Add(evaluationModel);
            }
            return new EvaluationTemplateDto
            {
                TemplateId = template.TemplateId,
                TemplateName = template.TemplateName,
                TemplateDescription = template.TemplateDescription,
                sceneDtos = evaluationSceneDtos,
                indicatorDtos = evaluationIndicators,
                ConfigDto = configModel
            };
        }

        /// <summary>
        /// 修改
        /// </summary>
        /// <param name="requestDto"></param>
        /// <returns></returns>
        public async Task<EvaluationTemplateUpdateResponseDto> UpdateAsync(EvaluationTemplateUpdateRequestDto requestDto)
        {
            EvaluationTemplateUpdateResponseDto responseDto = new EvaluationTemplateUpdateResponseDto();
            try
            {
                var result = await _db.Ado.UseTranAsync(async () =>
                {

                    var resultModel = await _repositorySaasEt.GetFirstAsync(a => a.TemplateId == requestDto.Model.TemplateId && !a.IsDisable);
                    if (resultModel == null)
                    {
                        throw new Exception("无法修改，请检查该评价模板是否存在！");
                    }
                    resultModel.TemplateName = requestDto.Model.TemplateName;
                    resultModel.TemplateDescription = requestDto.Model.TemplateDescription;
                    resultModel.ModifiedTime = DateTime.Now;
                    await _db.Updateable(resultModel).ExecuteCommandAsync();//修改评价模板

                    var evaluationContentConfig = await _repositorySaasEcc.GetFirstAsync(a => a.TemplateID == requestDto.Model.TemplateId);
                    if (evaluationContentConfig != null)
                    {
                        evaluationContentConfig.IsTextEvaluation = requestDto.ConfigCreateDto.IsTextEvaluation;
                        evaluationContentConfig.IsImageEvaluation = requestDto.ConfigCreateDto.IsImageEvaluation;
                        evaluationContentConfig.IsVideoEvaluation = requestDto.ConfigCreateDto.IsVideoEvaluation;
                        evaluationContentConfig.IsRequired = requestDto.ConfigCreateDto.IsRequired;
                        evaluationContentConfig.GuideText = requestDto.ConfigCreateDto.GuideText;
                        await _db.Updateable(evaluationContentConfig).ExecuteCommandAsync();//修改评级内容配置
                    }
                    else
                    {
                        var contentConfig = new EvaluationContentConfig()
                        {
                            TemplateID = requestDto.Model.TemplateId,
                            IsTextEvaluation = requestDto.ConfigCreateDto.IsTextEvaluation,
                            IsImageEvaluation = requestDto.ConfigCreateDto.IsImageEvaluation,
                            IsVideoEvaluation = requestDto.ConfigCreateDto.IsVideoEvaluation,
                            IsRequired = requestDto.ConfigCreateDto.IsRequired,
                            GuideText = requestDto.ConfigCreateDto.GuideText,
                        };
                        await _db.Insertable(contentConfig).ExecuteCommandAsync();//新增评价内容配置
                    }

                    // var IndicatorRelationList = new List<TemplateIndicatorRelation>();
                    var evaluationIndicators = await _db.Queryable<TemplateIndicatorRelation>()
                    .Where(c => c.TemplateId == requestDto.Model.TemplateId).Select(a => new EvaluationIndicator()
                    {
                        IndicatorId = a.IndicatorId

                    }).ToListAsync();//查询关系信息
                    //先删除绑定关系
                    await _db.Deleteable<TemplateIndicatorRelation>().AS("[MK_TemplateIndicatorRelation]").Where("TemplateId=@TemplateId", new { TemplateId = requestDto.Model.TemplateId }).ExecuteCommandAsync();
                    foreach (var evaluation in evaluationIndicators)
                    {
                        //删除指标明细
                        await _db.Deleteable<EvaluationIndicatorDetails>().AS("[MK_EvaluationIndicatorDetails]").Where("IndicatorID=@IndicatorID", new { IndicatorID = evaluation.IndicatorId }).ExecuteCommandAsync();
                    }
                    await _db.Deleteable(evaluationIndicators).ExecuteCommandAsync();//批量删除指标记录
                    //删除模板场景关联表
                    await _db.Deleteable<SceneTemplateRelation>().AS("[MK_SceneTemplateRelation]").Where("TemplateId=@TemplateId", new { TemplateId = requestDto.Model.TemplateId }).ExecuteCommandAsync();

                    var sceneTemplates = new List<SceneTemplateRelation>();
                    foreach (var sceneCreateDto in requestDto.sceneCreateDtos)
                    {
                        var sceneTemplate = new SceneTemplateRelation()
                        {
                            SceneId = sceneCreateDto.SceneId,
                            TemplateId = requestDto.Model.TemplateId
                        };
                        sceneTemplates.Add(sceneTemplate);
                    }
                    await _db.Insertable(sceneTemplates).ExecuteCommandAsync();//新增模板场景关系表
                    int so = 1;
                    foreach (var item in requestDto.IndicatorCreateDto)
                    {
                        var IndicatorModel = new EvaluationIndicator()
                        {
                            IndicatorName = item.IndicatorName,
                            IndicatorType = item.IndicatorType,
                            IndicatorDescription = item.IndicatorDescription,
                            IsRequired = item.IsRequired
                        };
                        var eid = await _db.Insertable(IndicatorModel).ExecuteReturnIdentityAsync();//新增模板指标项，返回自增ID
                        var indicatorRelationModel = new TemplateIndicatorRelation()
                        {
                            TemplateId = requestDto.Model.TemplateId,
                            IndicatorId = eid,
                            SortOrder = item.SortOrder
                        };
                        //IndicatorRelationList.Add(indicatorRelationModel);
                        var Inid = await _db.Insertable(indicatorRelationModel).ExecuteReturnIdentityAsync();//新增模板指标关系表
                        if (item.detailsCreateDtos.Count > 0)
                        {
                            var indicatorDetails = new List<EvaluationIndicatorDetails>();
                            foreach (var details in item.detailsCreateDtos)
                            {
                                var detailsModel = new EvaluationIndicatorDetails()
                                {
                                    IndicatorID = Inid,
                                    DetailsIDName = details.DetailsIDName,
                                };
                                indicatorDetails.Add(detailsModel);
                            }
                            await _db.Insertable(indicatorDetails).ExecuteCommandAsync();//新增指标明细表
                        }
                        so++;
                    }

                });

                if (result.IsSuccess)
                {
                    responseDto.Index = 1;
                }
                else
                {
                    responseDto.Index = 0;
                }
            }
            catch (Exception ex)
            {

                throw new Exception("修改失败：" + ex.Message);
            }
            return responseDto;
        }
    }
}
