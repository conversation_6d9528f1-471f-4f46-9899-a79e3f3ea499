﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using HdProject.Application.Services.Interfaces.SaasPos.MarketingManagement.Evaluation;
using HdProject.Domain.Context.SaasPos.MarketingManagement.Evaluation.EvaluationScene;
using HdProject.Domain.DTOs.SaasPos.MarketingManagement.Evaluation.EvaluationScene;
using HdProject.Domain.Entities.SaasPos.MarketingManagement.Evaluation;
using HdProject.Domain.Interfaces;
using LinqKit;

namespace HdProject.Application.Services.SaasPos.MarketingManagement.Evaluation
{
    public class EvaluationSceneService : IEvaluationSceneService
    {
        private readonly IRepositorySaas<Scene> _repositorySaasScene;//场景
        public EvaluationSceneService(IRepositorySaas<Scene> repositorySaasScene)
        {
            _repositorySaasScene = repositorySaasScene;
        }
        /// <summary>
        /// 查询全部信息
        /// </summary>
        /// <returns></returns>
        /// <exception cref="NotImplementedException"></exception>
        public async Task<EvaluationSceneGetAllResponseDto> GetAllAsync()
        {
            EvaluationSceneGetAllResponseDto responseDto = new EvaluationSceneGetAllResponseDto();
            var result = await _repositorySaasScene.GetListAsync();
            var model = result.Select(a => new EvaluationSceneDto
            {
                SceneId = a.SceneId,
                SceneName = a.SceneName, 
                SceneDescription = a.SceneDescription,
            }).ToList();
            responseDto.Model = model;
            return responseDto;
        }
    }
}
