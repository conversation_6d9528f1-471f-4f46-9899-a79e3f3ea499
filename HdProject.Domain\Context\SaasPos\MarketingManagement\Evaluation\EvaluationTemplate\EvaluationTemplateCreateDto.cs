﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using HdProject.Domain.Context.SaasPos.MarketingManagement.Evaluation.EvaluationIndicator;
using SqlSugar;

namespace HdProject.Domain.Context.SaasPos.MarketingManagement.Evaluation.EvaluationTemplate
{
    /// <summary>
    /// 评价模板新增DTO
    /// </summary>
    public class EvaluationTemplateCreateDto
    {
        /// <summary>
        /// 模板ID，自增主键
        /// </summary>
        public int TemplateId { get; set; }
        /// <summary>
        /// 模板名称
        /// </summary>
        public string TemplateName { get; set; }

        /// <summary>
        /// 模板描述
        /// </summary>
        public string? TemplateDescription { get; set; }


    }
}
