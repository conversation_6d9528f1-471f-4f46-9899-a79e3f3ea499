﻿using HdProject.Application.Services.Interfaces.CommodityManagement;
using HdProject.Application.Services.Interfaces.Rms;
using HdProject.Application.Services.Rms;
using HdProject.Domain.Context;
using HdProject.Domain.Context.MarketingActivityManagement;
using HdProject.Domain.Context.RMS;
using HdProject.Domain.Interfaces;
using HdProject.Infrastructure.Repositorys.Imp;
using HdProject.Web.Core;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using static HdProject.Domain.Context.MarketingActivityManagement.MarketingActivityContext;

namespace HdProject.Web.Api.Core.Controllers.MarketingActivityManagement
{
    [Route("[controller]/[Action]")]
    public class MarketingActivityController : PublicControllerBase
    {
        private readonly IWeChatCloudDbService _weChatCloudDbService;
        private readonly IMarketingActivityService _marketingActivityService;

        public MarketingActivityController(

            IMarketingActivityService marketingActivityService, IWeChatCloudDbService weChatCloudDbService)
        { _marketingActivityService = marketingActivityService; _weChatCloudDbService = weChatCloudDbService; }


        /// <summary>
        /// 获取销售和卡卷管理信息
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        [HttpGet]
        public async Task<IActionResult> GetSalesAndCardsInfoRecord([FromQuery] GetSalesAndCardsInfoContext context)
        {
            var res = await _marketingActivityService.GetSalesAndCardsInfoRecord(context);
            var results = JsonConvert.SerializeObject(res);
            return Ok(results);

        }
        /// <summary>
        /// 新增、修改销售和卡卷管理信息
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<IActionResult> EditorSalesAndCardsInfoRecord( EditorSalesAndCardsInfoContext context)
        {
            var res = await _marketingActivityService.EditorSalesAndCardsInfoRecord(context);
            return ApiData(res);
        }
        /// <summary>
        /// 删除销售和卡卷管理信息
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<IActionResult> DeleteSalesAndCardsInfoRecord(DeleteSalesAndCardsInfoContext context)
        {
            var res = await _marketingActivityService.DeleteSalesAndCardsInfoRecord(context);
            return ApiData(res);
        }

        /// <summary>
        /// 获取卡卷列表
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        [HttpGet]
        public async Task<IActionResult> GetCardSheetListRecord([FromQuery] GetCardSheetListContext context)
        {
            var res = await _marketingActivityService.GetCardSheetListRecord(context);
            return ApiPaged(res, context.Paging);

        }
        //获取微信小程序二维码
        [HttpGet]
        public async Task<IActionResult> GetWXQrCodeRecord([FromQuery] GetWXQrCodeContext context)
        {
            try
            {
                var qrCodeBytes = await _weChatCloudDbService.GenerateMiniProgramQrCodeAsync(context.Path);
                return File(qrCodeBytes, "image/jpeg"); // 直接返回图片文件流
            }
            catch (Exception ex)
            {
                return StatusCode(500, $"生成二维码失败: {ex.Message}");
            }
        }

    }
}
