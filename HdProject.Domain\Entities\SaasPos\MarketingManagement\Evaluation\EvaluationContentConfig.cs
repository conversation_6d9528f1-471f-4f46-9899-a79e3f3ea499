﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using SqlSugar;

namespace HdProject.Domain.Entities.SaasPos.MarketingManagement.Evaluation
{
    
    /// <summary>
    /// 评价内容配置表
    /// </summary>
    /// 
    [SugarTable("MK_EvaluationContentConfig")]
    public class EvaluationContentConfig
    {
        /// <summary>
        /// 自增主键
        /// </summary>
        [SugarColumn(IsPrimaryKey = true, IsIdentity = true)]
        public int ContentConfigID { get; set; }

        /// <summary>
        /// 模板外键
        /// </summary>
        public int TemplateID { get; set; }

        /// <summary>
        /// 是否文字评价
        /// </summary>
        public bool IsTextEvaluation { get; set; }
        /// <summary>
        /// 是否图片评价
        /// </summary>
        public bool IsImageEvaluation { get; set; }

        /// <summary>
        /// 是否视频评价
        /// </summary>
        public bool IsVideoEvaluation { get; set; }
        /// <summary>
        /// 是否必填
        /// </summary>
        public bool IsRequired { get; set; }
        /// <summary>
        /// 文字评价引导内容
        /// </summary>
        public string? GuideText { get; set; }

    }
}
