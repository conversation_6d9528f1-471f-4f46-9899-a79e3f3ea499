﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using SqlSugar;

namespace HdProject.Domain.Entities.SaasPos.MarketingManagement.Evaluation
{
    /// <summary>
    /// 评价内容表模型
    /// </summary>
    [SugarTable("MK_EvaluationContent")]
    public class EvaluationContent
    {
        /// <summary>
        /// 评价内容ID，自增主键
        /// </summary>
        [SugarColumn(ColumnName = "ContentID", IsPrimaryKey = true, IsIdentity = true)]
        public int ContentId { get; set; }
        /// <summary>
        /// 用户ID，记录评价的用户
        /// </summary>
        public int UserId { get; set; }

        /// <summary>
        /// 场景ID，表明在哪个场景下的评价
        /// </summary>
        public int SceneId { get; set; }

        /// <summary>
        /// 模板ID，使用的评价模板
        /// </summary>
        public int TemplateId { get; set; }

        /// <summary>
        /// 指标ID，针对哪个指标的评价
        /// </summary>
        public int IndicatorId { get; set; }

        /// <summary>
        /// 评分，若为星级评分等有分值的情况
        /// </summary>
        public int? Score { get; set; }

        /// <summary>
        /// 文字评价内容
        /// </summary>
        public string? Comment { get; set; }

        /// <summary>
        /// 评价时间，默认当前时间
        /// </summary>
        public DateTime? EvaluateTime { get; set; }
        /// <summary>
        /// 唯一回复ID
        /// </summary>
        public string? UniqueReplyID { get; set; }

    }

}
