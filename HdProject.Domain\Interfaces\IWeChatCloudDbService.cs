﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using HdProject.Domain.Context.WeChat;
using Microsoft.AspNetCore.Http;

namespace HdProject.Domain.Interfaces
{
    /// 微信云数据库服务接口
    /// </summary>
    public interface IWeChatCloudDbService
    {
        /// <summary>
        /// 获取微信接口调用凭证AccessToken
        /// </summary>
        Task<string> GetAccessTokenAsync();

        /// <summary>
        /// 执行微信云数据库查询
        /// </summary>
        /// <param name="query">数据库查询语句</param>
        Task<string> QueryDatabaseAsync(string query);

        /// <summary>
        /// 执行微信云数据库命令（修改）
        /// </summary>
        /// <param name="command">数据库操作命令</param>
        Task<string> ExecuteDatabaseCommandAsync(string command);
        /// <summary>
        /// 执行微信云数据库命令（新增）
        /// </summary>
        /// <param name="command"></param>
        /// <returns></returns>
        Task<string> AddeDatabaseCommandAsync(string command);
        /// <summary>
        /// 获取文件上传链接
        /// </summary>
        /// <param name="file"></param>
        /// <param name="fileName"></param>
        /// <returns></returns>
        Task<string> UploadImageToCloudStorage(IFormFile file, string fileName);

        Task<int> CompleteCloudStorageUpload(IFormFile file, string uploadResponseJson);
        /// <summary>
        /// 获取文件下载链接
        /// </summary>
        /// <param name="file"></param>
        /// <param name="fileName"></param>
        /// <returns></returns>
        Task<string> DownloadImageToCloudStorage(string fileId);
        /// <summary>
        /// 生成无限制的小程序码
        /// </summary>
        /// <param name="fileId"></param>
        /// <returns></returns>
        Task<byte[]> GenerateMiniProgramQrCodeAsync(string fullPath);
        /// <summary>
        /// 申请退款
        /// </summary>
        /// <param name="fullPath"></param>
        /// <returns></returns>
        Task<string> ApplyRefund(RefundParams query);
        /// <summary>
        /// 调用指定云函数
        /// </summary>
        /// <param name="functionName"></param>
        /// <param name="data"></param>
        /// <returns></returns>
        Task<string> invokeCloudFunction(string functionName, object data);
        /// <summary>
        /// 微信大学生认证
        /// </summary>
        /// <param name="openid"></param>
        /// <param name="wxStudentCheckCode"></param>
        /// <returns></returns>
        Task<string> QuickCheckStudentIdentity(string Openid, string WxStudentCheckCode);

    }
}
