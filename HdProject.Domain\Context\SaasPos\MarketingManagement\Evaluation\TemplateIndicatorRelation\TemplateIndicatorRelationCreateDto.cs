﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using HdProject.Domain.Context.SaasPos.MarketingManagement.Evaluation.EvaluationIndicator;
using SqlSugar;

namespace HdProject.Domain.Context.SaasPos.MarketingManagement.Evaluation.TemplateIndicatorRelation
{
    public class TemplateIndicatorRelationCreateDto
    {
        /// <summary>
        /// 关联关系ID，自增主键
        /// </summary>
        public int RelationId { get; set; }
        /// <summary>
        /// 外键，关联评价模板表的TemplateID
        /// </summary>
        public int TemplateId { get; set; }

        /// <summary>
        /// 外键，关联评价指标表的IndicatorID
        /// </summary>
        public int IndicatorId { get; set; }

        /// <summary>
        /// 指标在模板中的显示顺序
        /// </summary>
        public int SortOrder { get; set; }
        /// <summary>
        /// 指标对象
        /// </summary>
        public EvaluationIndicatorCreateDto IndicatorCreateDto { get; set; }
    }
}
