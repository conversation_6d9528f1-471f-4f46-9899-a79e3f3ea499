﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using HdProject.Domain.Context.SaasPos.MarketingManagement.Evaluation.EvaluationIndicator;

namespace HdProject.Domain.Context.SaasPos.MarketingManagement.Evaluation.TemplateIndicatorRelation
{
    /// <summary>
    /// 查询
    /// </summary>
    public class TemplateIndicatorRelationDto
    {
        /// <summary>
        /// 关联关系ID，自增主键
        /// </summary>
        public int RelationId { get; set; }
        /// <summary>
        /// 外键，关联评价模板表的TemplateID
        /// </summary>
        public int TemplateId { get; set; }

        /// <summary>
        /// 外键，关联评价指标表的IndicatorID
        /// </summary>
        public int IndicatorId { get; set; }

        /// <summary>
        /// 指标在模板中的显示顺序
        /// </summary>
        public int SortOrder { get; set; }

        //public EvaluationIndicatorDto IndicatorDto { get; set; }
    }
}
