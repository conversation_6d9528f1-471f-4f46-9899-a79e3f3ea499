﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using SqlSugar;

namespace HdProject.Domain.Entities.SaasPos.MarketingManagement.Evaluation
{
    /// <summary>
    /// 场景-模板关联表模型
    /// </summary>
    [SugarTable("MK_SceneTemplateRelation")]
    public class SceneTemplateRelation
    {
        /// <summary>
        /// 关联ID，自增主键
        /// </summary>
        [SugarColumn(ColumnName = "LinkID", IsPrimaryKey = true, IsIdentity = true)]
        public int LinkId { get; set; }

        /// <summary>
        /// 外键，关联场景表的SceneID
        /// </summary>
        public int SceneId { get; set; }

        /// <summary>
        /// 外键，关联评价模板表的TemplateID
        /// </summary>
        public int TemplateId { get; set; }

    }

}
