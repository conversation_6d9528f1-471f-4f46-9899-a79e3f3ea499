﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using HdProject.Domain.Context.SaasPos.MarketingManagement.Evaluation.EvaluationTemplate;
using HdProject.Domain.DTOs.SaasPos.MarketingManagement.Evaluation.EvaluationTemplate;

namespace HdProject.Application.Services.Interfaces.SaasPos.MarketingManagement.Evaluation
{
    /// <summary>
    /// 评价模板接口
    /// </summary>
    public interface IEvaluationTemplateService
    {
        /// <summary>
        /// 根据ID查询信息
        /// </summary>
        /// <param name="requestDto"></param>
        /// <returns></returns>
        Task<EvaluationTemplateGetByIdResponseDto> GetByIdAsync(EvaluationTemplateGetByIdRequestDto requestDto);
        /// <summary>
        /// 查询全部信息
        /// </summary>
        /// <returns></returns>
        Task<EvaluationTemplateGetAllResponseDto> GetAllAsync(EvaluationTemplateGetAllRequestDto requestDto);
        /// <summary>
        /// 新增信息
        /// </summary>
        /// <param name="bookDto"></param>
        /// <returns></returns>
        Task<EvaluationTemplateAddResponseDto> AddAsync(EvaluationTemplateAddRequestDto requestDto);


        /// <summary>
        /// 修改信息
        /// </summary>
        /// <param name="id"></param>
        /// <param name="bookDto"></param>
        /// <returns></returns>
        Task<EvaluationTemplateUpdateResponseDto> UpdateAsync(EvaluationTemplateUpdateRequestDto requestDto);
        /// <summary>
        /// 删除信息
        /// </summary>
        /// <param name="requestDto"></param>
        /// <returns></returns>
        Task<EvaluationTemplateDeleteResponseDto> DeleteAsync(EvaluationTemplateDeleteRequestDto requestDto);
    }
}
