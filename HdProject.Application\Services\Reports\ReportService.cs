using System.Globalization;
using System.Data;
using HdProject.Application.Services.Interfaces.Reports;
using HdProject.Domain.Context.Reports;
using SqlSugar;
using ClosedXML.Excel;
using System.Text.RegularExpressions;

namespace HdProject.Application.Services.Reports
{
    public class ReportService : IReportService
    {
        private readonly IBankSummaryDal _dal;
        private readonly ISqlSugarClient _sqlSugarClient;

        public ReportService(IBankSummaryDal dal, ISqlSugarClient sqlSugarClient)
        {
            _dal = dal;
            _sqlSugarClient = sqlSugarClient;
        }

        public async Task<BankSummaryResponseDto> GenerateBankSummaryReportAsync(BankSummaryRequestDto request)
        {
            // 1) 解析日期
            var (startDate, endDate) = ParseDateRange(request.StartDate, request.EndDate);

            // 2) 执行 SQL（扁平结果）
            var flat = await _dal.GetFlatAsync(startDate, endDate, request.BankSKs);

            // 3) 塑形为响应结构
            return Pivot(flat);
        }

        public async Task<(byte[] Bytes, string FileName, string ContentType)> ExportBankSummaryXlsxAsync(BankSummaryRequestDto request, string? titleDateRange = null)
        {
            var data = await GenerateBankSummaryReportAsync(request);

            using var wb = new XLWorkbook();
            var ws = wb.Worksheets.Add("银行汇总表");

            int col = 1;
            int row = 1;

            // 标题（格式：yyyy年M月d日），不展示“银行：”
            var (start, end) = ParseDateRange(request.StartDate, request.EndDate);
            var startZh = start.ToString("yyyy年M月d日");
            var endZh = end.ToString("yyyy年M月d日");

            ws.Cell(row, col).Value = $"日期：{startZh} 至 {endZh}";
            ws.Range(row, col, row, 1 + data.ColumnHeaders.Count * 5 + 4).Merge().Style
                .Font.SetBold().Font.SetFontSize(12);
            row += 2;

            // 叠表头 第一行
            ws.Cell(row, col).Value = "银行券名称/门店";
            ws.Range(row, col, row + 1, col).Merge().Style.Alignment.SetVertical(XLAlignmentVerticalValues.Center)
              .Alignment.SetHorizontal(XLAlignmentHorizontalValues.Center);

            col++;
            foreach (var shop in data.ColumnHeaders)
            {
                ws.Cell(row, col).Value = shop.ShopName;
                // 每店 5 列：核销量、核销额、补贴额、服务费、合计(核销额+补贴额+服务费)
                ws.Range(row, col, row, col + 4).Merge().Style.Alignment.SetHorizontal(XLAlignmentHorizontalValues.Center);
                ws.Cell(row + 1, col).Value = "核销量";
                ws.Cell(row + 1, col + 1).Value = "核销额";
                ws.Cell(row + 1, col + 2).Value = "补贴额";
                ws.Cell(row + 1, col + 3).Value = "服务费";
                ws.Cell(row + 1, col + 4).Value = "合计";
                col += 5;
            }

            // 尾部汇总四列
            ws.Cell(row, col).Value = "合计核销额";
            ws.Cell(row, col + 1).Value = "补贴金额";
            ws.Cell(row, col + 2).Value = "平台服务费";
            ws.Cell(row, col + 3).Value = "合计实收金额";
            ws.Range(row, col, row, col + 3).Style.Alignment.SetHorizontal(XLAlignmentHorizontalValues.Center);

            row += 2;

            // 数据行
            foreach (var r in data.Rows)
            {
                col = 1;
                ws.Cell(row, col++).Value = r.DealName;
                foreach (var shop in data.ColumnHeaders)
                {
                    r.ShopData.TryGetValue(shop.ShopID, out var cell);
                    var count = cell?.Count ?? 0;
                    var amount = cell?.Amount ?? 0m;
                    var subsidy = cell?.Subsidy ?? 0m;
                    var platformFee = cell?.PlatformFee ?? 0m;
                    ws.Cell(row, col++).Value = count;
                    ws.Cell(row, col++).Value = amount;
                    ws.Cell(row, col++).Value = subsidy;
                    ws.Cell(row, col++).Value = platformFee;
                    ws.Cell(row, col++).Value = amount + subsidy - platformFee;
                }
                // 行尾合计：使用 RowTotal 中的 Amount 和 Subsidy
                var rowAmtSum = r.RowTotal.Amount;
                var rowSubSum = r.RowTotal.Subsidy;
                var rowFeeSum = r.RowTotal.PlatformFee;
                var rowNetSum = r.RowTotal.NetAmount;
                ws.Cell(row, col++).Value = rowAmtSum;
                ws.Cell(row, col++).Value = rowSubSum;
                ws.Cell(row, col++).Value = rowFeeSum;
                ws.Cell(row, col++).Value = rowNetSum;
                row++;
            }

            // 合计行
            col = 1;
            ws.Cell(row, col).Value = "合计";
            ws.Range(row, col, row, col).Style.Font.SetBold();
            col++;
            foreach (var shop in data.ColumnHeaders)
            {
                var cnt = data.Rows.Sum(rr => rr.ShopData.TryGetValue(shop.ShopID, out var c) ? c.Count : 0);
                var amt = data.Rows.Sum(rr => rr.ShopData.TryGetValue(shop.ShopID, out var c) ? c.Amount : 0m);
                var sub = data.Rows.Sum(rr => rr.ShopData.TryGetValue(shop.ShopID, out var c) ? c.Subsidy : 0m);
                var fee = data.Rows.Sum(rr => rr.ShopData.TryGetValue(shop.ShopID, out var c) ? c.PlatformFee : 0m);
                var net = data.Rows.Sum(rr => rr.ShopData.TryGetValue(shop.ShopID, out var c) ? c.NetAmount : 0m);
                ws.Cell(row, col++).Value = cnt;
                ws.Cell(row, col++).Value = amt;
                ws.Cell(row, col++).Value = sub;
                ws.Cell(row, col++).Value = fee;
                ws.Cell(row, col++).Value = net;
            }
            ws.Cell(row, col++).Value = data.GrandTotal.TotalAmount;
            ws.Cell(row, col++).Value = data.GrandTotal.TotalSubsidy;
            ws.Cell(row, col++).Value = data.GrandTotal.TotalPlatformFee;
            ws.Cell(row, col++).Value = data.GrandTotal.TotalNetAmount;

            // 样式
            var lastCol = 1 + data.ColumnHeaders.Count * 5 + 4;
            var lastRow = row;
            ws.Range(3, 1, lastRow, lastCol).Style.Border.OutsideBorder = XLBorderStyleValues.Thin;
            ws.Range(3, 1, lastRow, lastCol).Style.Border.InsideBorder = XLBorderStyleValues.Thin;
            ws.Columns().AdjustToContents();

            using var ms = new MemoryStream();
            wb.SaveAs(ms);
            var bytes = ms.ToArray();
            var fileName = $"银行汇总表_{request.StartDate}_to_{request.EndDate}.xlsx";
            return (bytes, fileName, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        }

        private static BankSummaryResponseDto Pivot(List<FlatReportDataDto> flat)
        {
            var resp = new BankSummaryResponseDto();

            // 列头（去重的门店）
            var shops = flat
                .GroupBy(x => new { x.ShopID, x.ShopName })
                .Select(g => new ShopHeaderDto { ShopID = g.Key.ShopID, ShopName = g.Key.ShopName })
                .OrderBy(s => s.ShopName)
                .ToList();
            resp.ColumnHeaders = shops;

            // 行（券）
            var byDeal = flat.GroupBy(x => new { x.DealSK, x.DealName })
                .OrderBy(g => g.Key.DealName);

            foreach (var dealGroup in byDeal)
            {
                var row = new BankSummaryRowDto
                {
                    DealSK = dealGroup.Key.DealSK,
                    DealName = dealGroup.Key.DealName,
                    ShopData = new Dictionary<int, ShopCellDto>()
                };

                // 按 ShopID 对 dealGroup 进行分组，以提高查找效率
                var dealGroupByShop = dealGroup.ToLookup(x => x.ShopID);

                int rowCount = 0;
                decimal rowAmount = 0m, rowSubsidy = 0m, rowPlatformFee = 0m, rowNetAmount = 0m;

                // 为所有列头填充
                foreach (var shop in shops)
                {
                    // 使用 ToLookup 提高查找效率
                    var cellAgg = dealGroupByShop[shop.ShopID];
                    var cell = new ShopCellDto
                    {
                        Count = cellAgg.Sum(x => x.TotalCount),
                        Amount = cellAgg.Sum(x => x.TotalAmount),
                        Subsidy = cellAgg.Sum(x => x.TotalSubsidy),
                        PlatformFee = cellAgg.Sum(x => x.TotalPlatformFee),
                        NetAmount = cellAgg.Sum(x => x.TotalNetAmount)
                    };
                    row.ShopData[shop.ShopID] = cell;

                    rowCount += cell.Count;
                    rowAmount += cell.Amount;
                    rowSubsidy += cell.Subsidy;
                    rowPlatformFee += cell.PlatformFee;
                    rowNetAmount += cell.NetAmount;
                }

                row.RowTotal = new ShopTotalsDto { Count = rowCount, Amount = rowAmount, Subsidy = rowSubsidy, PlatformFee = rowPlatformFee, NetAmount = rowNetAmount };
                resp.Rows.Add(row);
            }

            // 总计
            resp.GrandTotal = new BankSummaryGrandTotalDto
            {
                TotalAmount = flat.Sum(x => x.TotalAmount),
                TotalSubsidy = flat.Sum(x => x.TotalSubsidy),
                TotalPlatformFee = flat.Sum(x => x.TotalPlatformFee),
                TotalNetAmount = flat.Sum(x => x.TotalNetAmount)
            };

            return resp;
        }

        public async Task<(byte[] Bytes, string FileName, string ContentType)> ExportDynamicUnifiedDailyReportAsync(DynamicUnifiedDailyReportRequestDto request)
        {
            // 1) 参数验证和日期转换
            if (!DateTime.TryParseExact(request.BeginDate, "yyyyMMdd", CultureInfo.InvariantCulture, DateTimeStyles.None, out var beginDate))
                throw new ArgumentException("BeginDate 格式错误，应为 yyyyMMdd");
            if (!DateTime.TryParseExact(request.EndDate, "yyyyMMdd", CultureInfo.InvariantCulture, DateTimeStyles.None, out var endDate))
                throw new ArgumentException("EndDate 格式错误，应为 yyyyMMdd");

            // 2) 调用存储过程获取数据
            var operateDataDb = _sqlSugarClient.AsTenant().GetConnection("OperateData");
            var parameters = new List<SugarParameter>
            {
                new SugarParameter("@ShopId", request.ShopId),
                new SugarParameter("@BeginDate", request.BeginDate),
                new SugarParameter("@EndDate", request.EndDate)
            };

            // 使用DataTable方式获取数据，更稳定
            var dataTable = await operateDataDb.Ado.UseStoredProcedure()
                .GetDataTableAsync("dbo.usp_GenerateDynamicUnifiedDailyReport", parameters);

            // 3) 数据验证
            if (dataTable == null || dataTable.Rows.Count == 0)
            {
                throw new InvalidOperationException("未查询到数据");
            }

            // 将DataTable转换为Dictionary列表
            var rawData = new List<Dictionary<string, object>>();
            foreach (DataRow row in dataTable.Rows)
            {
                var dict = new Dictionary<string, object>();
                foreach (DataColumn column in dataTable.Columns)
                {
                    dict[column.ColumnName] = row[column] == DBNull.Value ? null : row[column];
                }
                rawData.Add(dict);
            }

            // 4) 生成Excel文件
            return GenerateExcelFile(rawData, request);
        }

        private (byte[] Bytes, string FileName, string ContentType) GenerateExcelFile(
            List<Dictionary<string, object>> rawData,
            DynamicUnifiedDailyReportRequestDto request)
        {
            using var wb = new XLWorkbook();
            var ws = wb.Worksheets.Add("营业报表");

            // 解析数据结构
            var processedData = ProcessDynamicData(rawData);

            // 生成表头和数据
            GenerateExcelHeaders(ws, processedData, request.Lang);
            GenerateExcelData(ws, processedData, request.Lang);

            // 设置样式
            ApplyExcelStyles(ws, processedData);

            // 保存文件
            using var ms = new MemoryStream();
            wb.SaveAs(ms);
            var bytes = ms.ToArray();

            var fileName = $"营业报表_{request.BeginDate}_to_{request.EndDate}.xlsx";
            return (bytes, fileName, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        }

        private ProcessedReportData ProcessDynamicData(List<Dictionary<string, object>> rawData)
        {
            var result = new ProcessedReportData
            {
                BaseData = new List<Dictionary<string, object>>(),
                TimeSlots = new List<TimeSlotInfo>()
            };

            var timeSlotFields = new Dictionary<string, Dictionary<string, List<object>>>();

            foreach (var row in rawData)
            {
                var baseRow = new Dictionary<string, object>();
                var timeSlotRow = new Dictionary<string, Dictionary<string, object>>();

                foreach (var kvp in row)
                {
                    // 匹配时间段字段格式: "HH:MM-HH:MM_字段名"
                    var timeMatch = Regex.Match(kvp.Key, @"^(\d{2}:\d{2}-\d{2}:\d{2})_(.+)$");

                    if (timeMatch.Success)
                    {
                        var timeSlot = timeMatch.Groups[1].Value;
                        var fieldName = timeMatch.Groups[2].Value;

                        if (!timeSlotFields.ContainsKey(timeSlot))
                            timeSlotFields[timeSlot] = new Dictionary<string, List<object>>();

                        if (!timeSlotFields[timeSlot].ContainsKey(fieldName))
                            timeSlotFields[timeSlot][fieldName] = new List<object>();

                        timeSlotFields[timeSlot][fieldName].Add(kvp.Value ?? 0);
                    }
                    else
                    {
                        baseRow[kvp.Key] = kvp.Value;
                    }
                }

                result.BaseData.Add(baseRow);
            }

            // 转换时间段数据
            foreach (var timeSlot in timeSlotFields.Keys.OrderBy(x => x))
            {
                result.TimeSlots.Add(new TimeSlotInfo
                {
                    TimeSlot = timeSlot,
                    Fields = timeSlotFields[timeSlot]
                });
            }

            return result;
        }

        private void GenerateExcelHeaders(IXLWorksheet ws, ProcessedReportData data, string lang)
        {
            var shopName = data.BaseData.FirstOrDefault()?.ContainsKey("ShopName") == true ?
                data.BaseData.First()["ShopName"]?.ToString() ?? "门店" : "门店";
            var dateRange = GetDateRangeFromData(data);

            // 第1行：标题行
            var title = $"【{shopName}】{dateRange}";
            ws.Cell(1, 1).Value = title;
            ws.Cell(1, 1).Style.Font.Bold = true;
            ws.Cell(1, 1).Style.Font.FontSize = 14;

            // 获取所有字段的分层结构
            var fieldStructure = GetFieldStructure(data);

            // 生成分层表头
            GenerateHierarchicalHeaders(ws, fieldStructure);
        }

        private Dictionary<string, object> GetFieldStructure(ProcessedReportData data)
        {
            // TODO: 当前方法硬编码了大量字段映射关系，不利于维护和扩展。
            // 建议未来考虑使用配置文件或数据库来存储这些映射关系，并在运行时读取。
            var structure = new Dictionary<string, object>();

            // 1. 基础信息分组
            var basicInfo = new Dictionary<string, object>();
            basicInfo["ReportDate"] = "日期";
            basicInfo["ShopName"] = "门店";
            basicInfo["Weekday"] = "星期";
            structure["基础信息"] = basicInfo;

            // 2. 营收数据分组
            var revenueData = new Dictionary<string, object>();
            revenueData["Revenue_Total"] = "总营业收入";
            revenueData["Revenue_DayTime"] = "白天档";
            revenueData["Revenue_NightTime"] = "晚档";
            structure["营收数据"] = revenueData;

            // 3. 带客数据分组
            var batchData = new Dictionary<string, object>();
            batchData["TotalBatchCount_AllDay"] = "全天总营客批数";
            batchData["DayTime_TotalBatch"] = "白天档K+餐";
            batchData["DayTime_DirectFallBatch"] = "白天档直落";
            batchData["NightTime_TotalBatch"] = "20点后进场";
            batchData["NightTime_DirectFallBatch"] = "晚档直落";
            structure["带客数据"] = batchData;

            // 4. 用餐人数分组
            var guestCount = new Dictionary<string, object>();
            guestCount["BuffetGuestCount"] = "K+餐人数";
            guestCount["TotalDirectFallGuests"] = "直落人数";
            structure["用餐人数"] = guestCount;

            // 5. 动态时间段分组（按时间顺序）
            foreach (var timeSlot in data.TimeSlots.OrderBy(ts => ts.TimeSlot))
            {
                var timeSlotGroup = new Dictionary<string, object>();

                // 定义时间段字段的标准顺序和中文名称
                var timeSlotFieldMappings = new Dictionary<string, string>
                {
                    { "KPlus", "K+" },
                    { "SpecialReservation", "特权预约" },
                    { "Meituan", "美团" },
                    { "Douyin", "抖音" },
                    { "RoomFee", "房费" },
                    { "PrevSlotDirectFall", "上一档直落批数" },
                    { "Subtotal", "小计" }
                };

                // 按标准顺序添加存在的字段
                foreach (var fieldMapping in timeSlotFieldMappings)
                {
                    if (timeSlot.Fields.ContainsKey(fieldMapping.Key))
                    {
                        timeSlotGroup[$"{timeSlot.TimeSlot}_{fieldMapping.Key}"] = fieldMapping.Value;
                    }
                }

                structure[timeSlot.TimeSlot] = timeSlotGroup;
            }

            // 6. K+餐数据分组
            var kPlusData = new Dictionary<string, object>();
            kPlusData["DayTime_KPlusMeal_BatchCount"] = "k+餐批次";
            kPlusData["DayTime_KPlusMeal_DirectFallBatch"] = "k+餐直落批数";
            kPlusData["NightTime_EarlySlots_DirectFallBatch"] = "17点18点19点档直落";
            structure["k+餐数据"] = kPlusData;

            // 7. K+自由餐分组
            var freemeal = new Dictionary<string, object>();
            freemeal["Night_FreeMeal_KPlus"] = "k+";
            freemeal["Night_FreeMeal_SpecialReservation"] = "特权预约";
            freemeal["Night_FreeMeal_Meituan"] = "美团";
            freemeal["Night_FreeMeal_Douyin"] = "抖音";
            freemeal["Night_FreeMeal_SubtotalBatch"] = "小计";
            freemeal["Night_FreeMeal_Revenue"] = "营业额";
            structure["k+自由餐"] = freemeal;

            // 8. 20点后分组
            var after8PM = new Dictionary<string, object>();
            after8PM["After8PM_BuyoutBatch"] = "买断批次";
            after8PM["After8PM_AllYouCanDrinkPackageBatch"] = "畅饮套餐";
            after8PM["After8PM_FreeConsumptionPackageBatch"] = "自由消套餐";
            after8PM["After8PM_Promo_SpecialReservation"] = "促销套餐特权预约";
            after8PM["After8PM_Promo_Meituan"] = "促销套餐美团";
            after8PM["After8PM_Promo_Douyin"] = "促销套餐抖音";
            after8PM["After8PM_RoomFeeBatch"] = "房费批次";
            after8PM["After8PM_OtherBatch"] = "其他批次";
            after8PM["After8PM_SubtotalBatch"] = "批次小计";
            after8PM["After8PM_Revenue"] = "营收金额";
            structure["20点后"] = after8PM;

            // 9. 招待分组
            var complimentary = new Dictionary<string, object>();
            complimentary["Complimentary_BatchCount"] = "招待批次";
            complimentary["Complimentary_Revenue"] = "招待金额";
            structure["招待"] = complimentary;

            return structure;
        }

        private void GenerateHierarchicalHeaders(IXLWorksheet ws, Dictionary<string, object> structure)
        {
            var headerRows = 3; // 使用3行表头
            var col = 1;

            // 递归生成表头
            GenerateHeaderLevel(ws, structure, 2, ref col, headerRows);

            // 设置表头样式
            ApplyHeaderStyles(ws, headerRows, col - 1);
        }

        private void GenerateHeaderLevel(IXLWorksheet ws, Dictionary<string, object> structure, int currentRow, ref int col, int maxRows)
        {
            foreach (var kvp in structure)
            {
                if (kvp.Value is Dictionary<string, object> subStructure)
                {
                    // 这是一个分组
                    var startCol = col;
                    var groupName = kvp.Key;

                    // 递归处理子级
                    GenerateHeaderLevel(ws, subStructure, currentRow + 1, ref col, maxRows);

                    // 设置分组标题并合并单元格
                    var endCol = col - 1;
                    if (endCol >= startCol)
                    {
                        ws.Cell(currentRow, startCol).Value = groupName;
                        ws.Range(currentRow, startCol, currentRow, endCol).Merge();
                        // 垂直居中
                        ws.Range(currentRow, startCol, currentRow, endCol).Style.Alignment.SetVertical(XLAlignmentVerticalValues.Center);
                    }
                }
                else
                {
                    // 这是一个字段
                    ws.Cell(currentRow, col).Value = kvp.Value.ToString();
                    // 如果不是最后一行，需要向下合并
                    if (currentRow < maxRows)
                    {
                        ws.Range(currentRow, col, maxRows, col).Merge();
                        // 垂直居中
                        ws.Range(currentRow, col, maxRows, col).Style.Alignment.SetVertical(XLAlignmentVerticalValues.Center);
                    }
                    col++;
                }
            }
        }

        private void GenerateExcelData(IXLWorksheet ws, ProcessedReportData data, string lang)
        {
            var fieldStructure = GetFieldStructure(data);
            var totalCols = CountTotalColumns(fieldStructure);
            var row = 4; // 从第4行开始写入数据

            for (int i = 0; i < data.BaseData.Count; i++)
            {
                var baseRow = data.BaseData[i];
                int col = 1; // 声明并初始化 col 变量
                WriteDataByStructure(ws, row, ref col, fieldStructure, baseRow, data, i);
                row++;
            }

            // 应用数据样式
            var dataRange = ws.Range(4, 1, row - 1, totalCols);
            dataRange.Style.Alignment.Horizontal = XLAlignmentHorizontalValues.Right;
        }

        private void WriteDataByStructure(IXLWorksheet ws, int row, ref int col, Dictionary<string, object> structure,
            Dictionary<string, object> baseRow, ProcessedReportData data, int rowIndex)
        {
            foreach (var kvp in structure)
            {
                if (kvp.Value is Dictionary<string, object> subStructure)
                {
                    // 递归处理子级
                    WriteDataByStructure(ws, row, ref col, subStructure, baseRow, data, rowIndex);
                }
                else
                {
                    // 这是一个字段，写入值
                    var fieldName = kvp.Key;
                    var value = GetFieldValue(fieldName, baseRow, data, rowIndex);
                    ws.Cell(row, col).Value = value;
                    col++;
                }
            }
        }

        private string GetFieldValue(string fieldName, Dictionary<string, object> baseRow, ProcessedReportData data, int rowIndex)
        {
            // 特殊处理 ReportDate 字段
            if (fieldName == "ReportDate" && baseRow.ContainsKey("ReportDate"))
            {
                return FormatReportDate(baseRow["ReportDate"]?.ToString());
            }

            // 处理基础字段
            if (baseRow.ContainsKey(fieldName))
            {
                return baseRow[fieldName]?.ToString() ?? "";
            }

            // 处理时间段字段
            if (IsTimeSlotPattern(fieldName) && data.TimeSlots.Any())
            {
                var parts = fieldName.Split('_');
                if (parts.Length == 2)
                {
                    var timeSlotKey = parts[0];
                    var fieldKey = parts[1];

                    // 查找对应的时间段
                    var timeSlotInfo = data.TimeSlots.FirstOrDefault(ts => ts.TimeSlot == timeSlotKey);
                    if (timeSlotInfo != null && timeSlotInfo.Fields.ContainsKey(fieldKey))
                    {
                        var values = timeSlotInfo.Fields[fieldKey];
                        // 使用rowIndex来获取对应行的值
                        if (rowIndex < values.Count)
                        {
                            return values[rowIndex]?.ToString() ?? "";
                        }
                        else if (values.Count > 0)
                        {
                            // 如果rowIndex超出范围，返回第一个值作为默认值
                            return values[0]?.ToString() ?? "";
                        }
                    }
                }
            }

            return "";
        }

        private bool IsTimeSlotPattern(string text)
        {
            return Regex.IsMatch(text, @"^\d{1,2}:\d{2}-(?:\d{1,2}:\d{2})_");
        }

        private void ApplyExcelStyles(IXLWorksheet ws, ProcessedReportData data)
        {
            var fieldStructure = GetFieldStructure(data);
            var totalCols = CountTotalColumns(fieldStructure);

            // 表头样式
            ApplyHeaderStyles(ws, 3, totalCols);

            // 数据区域样式
            var dataRange = ws.Range(4, 1, ws.LastRowUsed().RowNumber(), totalCols);
            dataRange.Style.Alignment.Horizontal = XLAlignmentHorizontalValues.Right;
            dataRange.Style.Alignment.Vertical = XLAlignmentVerticalValues.Center;
            dataRange.Style.Border.OutsideBorder = XLBorderStyleValues.Thin;
            dataRange.Style.Border.InsideBorder = XLBorderStyleValues.Thin;

            ws.Columns().AdjustToContents();
        }

        private int CountTotalColumns(Dictionary<string, object> structure)
        {
            int count = 0;
            foreach (var kvp in structure)
            {
                if (kvp.Value is Dictionary<string, object> subStructure)
                {
                    count += CountTotalColumns(subStructure);
                }
                else
                {
                    count++;
                }
            }
            return count;
        }

        private void ApplyHeaderStyles(IXLWorksheet ws, int headerRows, int totalCols)
        {
            var headerRange = ws.Range(1, 1, headerRows, totalCols);
            headerRange.Style.Alignment.Horizontal = XLAlignmentHorizontalValues.Center;
            headerRange.Style.Alignment.Vertical = XLAlignmentVerticalValues.Center;
            headerRange.Style.Font.Bold = true;
            headerRange.Style.Fill.BackgroundColor = XLColor.LightGray;
            headerRange.Style.Border.OutsideBorder = XLBorderStyleValues.Thin;
            headerRange.Style.Border.InsideBorder = XLBorderStyleValues.Thin;
        }

        private string GetDateRangeFromData(ProcessedReportData data)
        {
            var dates = data.BaseData
                .Where(row => row.ContainsKey("ReportDate"))
                .Select(row => FormatReportDate(row["ReportDate"]?.ToString()))
                .Where(date => !string.IsNullOrEmpty(date))
                .Distinct()
                .OrderBy(date => date)
                .ToList();

            if (dates.Count == 0)
                return "日期范围未知";
            else if (dates.Count == 1)
                return dates[0];
            else
                return $"{dates.First()} 至 {dates.Last()}";
        }

        private string FormatReportDate(string dateString)
        {
            if (string.IsNullOrEmpty(dateString))
                return "";

            // 处理 .NET 日期格式 /Date(timestamp)/
            if (dateString.Contains("/Date("))
            {
                var match = Regex.Match(dateString, @"/Date\((\d+)\)/");
                if (match.Success && long.TryParse(match.Groups[1].Value, out var timestamp))
                {
                    var dateTime = DateTimeOffset.FromUnixTimeMilliseconds(timestamp).DateTime;
                    return dateTime.ToString("yyyy-MM-dd");
                }
            }

            // 处理标准日期时间格式，去掉时间部分
            if (DateTime.TryParse(dateString, out var parsedDate))
            {
                return parsedDate.ToString("yyyy-MM-dd");
            }

            // 如果包含时间部分，尝试去掉 " 0:00:00" 或类似的时间后缀
            if (dateString.Contains(" "))
            {
                var datePart = dateString.Split(' ')[0];
                if (DateTime.TryParse(datePart, out var dateOnly))
                {
                    return dateOnly.ToString("yyyy-MM-dd");
                }
            }

            return dateString; // 如果无法解析，返回原始字符串
        }

        /// <summary>
        /// 解析日期范围字符串
        /// </summary>
        /// <param name="startDateStr">开始日期字符串 (yyyy-MM-dd)</param>
        /// <param name="endDateStr">结束日期字符串 (yyyy-MM-dd)</param>
        /// <returns>解析后的开始日期和结束日期元组</returns>
        /// <exception cref="ArgumentException">当日期字符串格式不正确时抛出</exception>
        private static (DateTime StartDate, DateTime EndDate) ParseDateRange(string startDateStr, string endDateStr)
        {
            if (!DateTime.TryParseExact(startDateStr, "yyyy-MM-dd", CultureInfo.InvariantCulture, DateTimeStyles.None, out var startDate))
                throw new ArgumentException("StartDate 格式错误，应为 yyyy-MM-dd");
            if (!DateTime.TryParseExact(endDateStr, "yyyy-MM-dd", CultureInfo.InvariantCulture, DateTimeStyles.None, out var endDate))
                throw new ArgumentException("EndDate 格式错误，应为 yyyy-MM-dd");

            return (startDate, endDate);
        }
    }
}
