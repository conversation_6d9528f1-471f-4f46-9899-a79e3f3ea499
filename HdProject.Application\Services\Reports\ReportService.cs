using System.Globalization;
using System.Data;
using HdProject.Application.Services.Interfaces.Reports;
using HdProject.Domain.Context.Reports;
using SqlSugar;
using ClosedXML.Excel;
using System.Text.RegularExpressions;

namespace HdProject.Application.Services.Reports
{
    // 辅助类定义
    public class ProcessedReportData
    {
        public List<Dictionary<string, object>> BaseData { get; set; } = new();
        public List<TimeSlotInfo> TimeSlots { get; set; } = new();
    }

    public class TimeSlotInfo
    {
        public string TimeSlot { get; set; } = string.Empty;
        public Dictionary<string, List<object>> Fields { get; set; } = new();
    }

    public class ReportService : IReportService
    {
        private readonly IBankSummaryDal _dal;
        private readonly ISqlSugarClient _sqlSugarClient;

        public ReportService(IBankSummaryDal dal, ISqlSugarClient sqlSugarClient)
        {
            _dal = dal;
            _sqlSugarClient = sqlSugarClient;
        }

        public async Task<BankSummaryResponseDto> GenerateBankSummaryReportAsync(BankSummaryRequestDto request)
        {
            // 1) 解析日期
            if (!DateTime.TryParseExact(request.StartDate, "yyyy-MM-dd", CultureInfo.InvariantCulture, DateTimeStyles.None, out var startDate))
                throw new ArgumentException("StartDate 格式错误，应为 yyyy-MM-dd");
            if (!DateTime.TryParseExact(request.EndDate, "yyyy-MM-dd", CultureInfo.InvariantCulture, DateTimeStyles.None, out var endDate))
                throw new ArgumentException("EndDate 格式错误，应为 yyyy-MM-dd");

            // 2) 执行 SQL（扁平结果）
            var flat = await _dal.GetFlatAsync(startDate, endDate, request.BankSKs);

            // 3) 塑形为响应结构
            return Pivot(flat);
        }

        public async Task<(byte[] Bytes, string FileName, string ContentType)> ExportBankSummaryXlsxAsync(BankSummaryRequestDto request, string? titleDateRange = null)
        {
            var data = await GenerateBankSummaryReportAsync(request);

            using var wb = new XLWorkbook();
            var ws = wb.Worksheets.Add("银行汇总表");

            int col = 1;
            int row = 1;

            // 标题（格式：yyyy年M月d日），不展示“银行：”
            if (!DateTime.TryParseExact(request.StartDate, "yyyy-MM-dd", CultureInfo.InvariantCulture, DateTimeStyles.None, out var start))
                throw new ArgumentException("StartDate 格式错误，应为 yyyy-MM-dd");
            if (!DateTime.TryParseExact(request.EndDate, "yyyy-MM-dd", CultureInfo.InvariantCulture, DateTimeStyles.None, out var end))
                throw new ArgumentException("EndDate 格式错误，应为 yyyy-MM-dd");
            var startZh = start.ToString("yyyy年M月d日");
            var endZh = end.ToString("yyyy年M月d日");

            ws.Cell(row, col).Value = $"日期：{startZh} 至 {endZh}";
            ws.Range(row, col, row, 1 + data.ColumnHeaders.Count * 5 + 4).Merge().Style
                .Font.SetBold().Font.SetFontSize(12);
            row += 2;

            // 叠表头 第一行
            ws.Cell(row, col).Value = "银行券名称/门店";
            ws.Range(row, col, row + 1, col).Merge().Style.Alignment.SetVertical(XLAlignmentVerticalValues.Center)
              .Alignment.SetHorizontal(XLAlignmentHorizontalValues.Center);

            col++;
            foreach (var shop in data.ColumnHeaders)
            {
                ws.Cell(row, col).Value = shop.ShopName;
                // 每店 5 列：核销量、核销额、补贴额、服务费、合计(核销额+补贴额+服务费)
                ws.Range(row, col, row, col + 4).Merge().Style.Alignment.SetHorizontal(XLAlignmentHorizontalValues.Center);
                ws.Cell(row + 1, col).Value = "核销量";
                ws.Cell(row + 1, col + 1).Value = "核销额";
                ws.Cell(row + 1, col + 2).Value = "补贴额";
                ws.Cell(row + 1, col + 3).Value = "服务费";
                ws.Cell(row + 1, col + 4).Value = "合计";
                col += 5;
            }

            // 尾部汇总四列
            ws.Cell(row, col).Value = "合计核销额";
            ws.Cell(row, col + 1).Value = "补贴金额";
            ws.Cell(row, col + 2).Value = "平台服务费";
            ws.Cell(row, col + 3).Value = "合计实收金额";
            ws.Range(row, col, row, col + 3).Style.Alignment.SetHorizontal(XLAlignmentHorizontalValues.Center);

            row += 2;

            // 数据行
            foreach (var r in data.Rows)
            {
                col = 1;
                ws.Cell(row, col++).Value = r.DealName;
                foreach (var shop in data.ColumnHeaders)
                {
                    r.ShopData.TryGetValue(shop.ShopID, out var cell);
                    var cnt = cell?.Count ?? 0;
                    var amt = cell?.Amount ?? 0m;
                    var sub = cell?.Subsidy ?? 0m;
                    var fee = cell?.PlatformFee ?? 0m;
                    ws.Cell(row, col++).Value = cnt;
                    ws.Cell(row, col++).Value = amt;
                    ws.Cell(row, col++).Value = sub;
                    ws.Cell(row, col++).Value = fee;
                    ws.Cell(row, col++).Value = amt + sub - fee;
                }
                // 行尾合计：使用 RowTotal 中的 Amount 和 Subsidy
                var rowAmtSum = r.RowTotal.Amount;
                var rowSubSum = r.RowTotal.Subsidy;
                var rowFeeSum = r.RowTotal.PlatformFee;
                var rowNetSum = r.RowTotal.NetAmount;
                ws.Cell(row, col++).Value = rowAmtSum;
                ws.Cell(row, col++).Value = rowSubSum;
                ws.Cell(row, col++).Value = rowFeeSum;
                ws.Cell(row, col++).Value = rowNetSum;
                row++;
            }

            // 合计行
            col = 1;
            ws.Cell(row, col).Value = "合计";
            ws.Range(row, col, row, col).Style.Font.SetBold();
            col++;
            foreach (var shop in data.ColumnHeaders)
            {
                var cnt = data.Rows.Sum(rr => rr.ShopData.TryGetValue(shop.ShopID, out var c) ? c.Count : 0);
                var amt = data.Rows.Sum(rr => rr.ShopData.TryGetValue(shop.ShopID, out var c) ? c.Amount : 0m);
                var sub = data.Rows.Sum(rr => rr.ShopData.TryGetValue(shop.ShopID, out var c) ? c.Subsidy : 0m);
                var fee = data.Rows.Sum(rr => rr.ShopData.TryGetValue(shop.ShopID, out var c) ? c.PlatformFee : 0m);
                var net = data.Rows.Sum(rr => rr.ShopData.TryGetValue(shop.ShopID, out var c) ? c.NetAmount : 0m);
                ws.Cell(row, col++).Value = cnt;
                ws.Cell(row, col++).Value = amt;
                ws.Cell(row, col++).Value = sub;
                ws.Cell(row, col++).Value = fee;
                ws.Cell(row, col++).Value = net;
            }
            ws.Cell(row, col++).Value = data.GrandTotal.TotalAmount;
            ws.Cell(row, col++).Value = data.GrandTotal.TotalSubsidy;
            ws.Cell(row, col++).Value = data.GrandTotal.TotalPlatformFee;
            ws.Cell(row, col++).Value = data.GrandTotal.TotalNetAmount;

            // 样式
            var lastCol = 1 + data.ColumnHeaders.Count * 5 + 4;
            var lastRow = row;
            ws.Range(3, 1, lastRow, lastCol).Style.Border.OutsideBorder = XLBorderStyleValues.Thin;
            ws.Range(3, 1, lastRow, lastCol).Style.Border.InsideBorder = XLBorderStyleValues.Thin;
            ws.Columns().AdjustToContents();

            using var ms = new MemoryStream();
            wb.SaveAs(ms);
            var bytes = ms.ToArray();
            var fileName = $"银行汇总表_{request.StartDate}_to_{request.EndDate}.xlsx";
            return (bytes, fileName, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        }

        private static BankSummaryResponseDto Pivot(List<FlatReportDataDto> flat)
        {
            var resp = new BankSummaryResponseDto();

            // 列头（去重的门店）
            var shops = flat
                .GroupBy(x => new { x.ShopID, x.ShopName })
                .Select(g => new ShopHeaderDto { ShopID = g.Key.ShopID, ShopName = g.Key.ShopName })
                .OrderBy(s => s.ShopName)
                .ToList();
            resp.ColumnHeaders = shops;

            // 行（券）
            var byDeal = flat.GroupBy(x => new { x.DealSK, x.DealName })
                .OrderBy(g => g.Key.DealName);

            foreach (var dealGroup in byDeal)
            {
                var row = new BankSummaryRowDto
                {
                    DealSK = dealGroup.Key.DealSK,
                    DealName = dealGroup.Key.DealName,
                    ShopData = new Dictionary<int, ShopCellDto>()
                };

                int rowCount = 0;
                decimal rowAmount = 0m, rowSubsidy = 0m, rowPlatformFee = 0m, rowNetAmount = 0m;

                // 为所有列头填充
                foreach (var shop in shops)
                {
                    var cellAgg = dealGroup.Where(x => x.ShopID == shop.ShopID);
                    var cell = new ShopCellDto
                    {
                        Count = cellAgg.Sum(x => x.TotalCount),
                        Amount = cellAgg.Sum(x => x.TotalAmount),
                        Subsidy = cellAgg.Sum(x => x.TotalSubsidy),
                        PlatformFee = cellAgg.Sum(x => x.TotalPlatformFee),
                        NetAmount = cellAgg.Sum(x => x.TotalNetAmount)
                    };
                    row.ShopData[shop.ShopID] = cell;

                    rowCount += cell.Count;
                    rowAmount += cell.Amount;
                    rowSubsidy += cell.Subsidy;
                    rowPlatformFee += cell.PlatformFee;
                    rowNetAmount += cell.NetAmount;
                }

                row.RowTotal = new ShopTotalsDto { Count = rowCount, Amount = rowAmount, Subsidy = rowSubsidy, PlatformFee = rowPlatformFee, NetAmount = rowNetAmount };
                resp.Rows.Add(row);
            }

            // 总计
            resp.GrandTotal = new BankSummaryGrandTotalDto
            {
                TotalAmount = flat.Sum(x => x.TotalAmount),
                TotalSubsidy = flat.Sum(x => x.TotalSubsidy),
                TotalPlatformFee = flat.Sum(x => x.TotalPlatformFee),
                TotalNetAmount = flat.Sum(x => x.TotalNetAmount)
            };

            return resp;
        }

        public async Task<(byte[] Bytes, string FileName, string ContentType)> ExportDynamicUnifiedDailyReportAsync(DynamicUnifiedDailyReportRequestDto request)
        {
            // 1) 参数验证和日期转换
            if (!DateTime.TryParseExact(request.BeginDate, "yyyyMMdd", CultureInfo.InvariantCulture, DateTimeStyles.None, out var beginDate))
                throw new ArgumentException("BeginDate 格式错误，应为 yyyyMMdd");
            if (!DateTime.TryParseExact(request.EndDate, "yyyyMMdd", CultureInfo.InvariantCulture, DateTimeStyles.None, out var endDate))
                throw new ArgumentException("EndDate 格式错误，应为 yyyyMMdd");

            // 2) 调用存储过程获取数据
            var operateDataDb = _sqlSugarClient.AsTenant().GetConnection("OperateData");
            var parameters = new List<SugarParameter>
            {
                new SugarParameter("@ShopId", request.ShopId),
                new SugarParameter("@BeginDate", request.BeginDate),
                new SugarParameter("@EndDate", request.EndDate)
            };

            // 使用DataTable方式获取数据，更稳定
            var dataTable = await operateDataDb.Ado.UseStoredProcedure()
                .GetDataTableAsync("dbo.usp_GenerateDynamicUnifiedDailyReport", parameters);

            // 3) 打印具体数据进行调试
            Console.WriteLine($"查询参数: ShopId={request.ShopId}, BeginDate={request.BeginDate}, EndDate={request.EndDate}");
            Console.WriteLine($"查询结果记录数: {dataTable?.Rows.Count ?? 0}");
            Console.WriteLine($"查询结果列数: {dataTable?.Columns.Count ?? 0}");

            if (dataTable != null && dataTable.Rows.Count > 0)
            {
                Console.WriteLine("原始数据结构:");
                Console.WriteLine("列名列表:");
                for (int i = 0; i < dataTable.Columns.Count; i++)
                {
                    Console.WriteLine($"  列{i + 1}: {dataTable.Columns[i].ColumnName} ({dataTable.Columns[i].DataType.Name})");
                }

                Console.WriteLine("\n数据内容:");
                for (int i = 0; i < Math.Min(dataTable.Rows.Count, 3); i++) // 只打印前3条记录
                {
                    Console.WriteLine($"第{i + 1}条记录:");
                    for (int j = 0; j < Math.Min(dataTable.Columns.Count, 10); j++) // 只打印前10列
                    {
                        var columnName = dataTable.Columns[j].ColumnName;
                        var value = dataTable.Rows[i][j];
                        Console.WriteLine($"  {columnName}: {value ?? "NULL"}");
                    }
                    Console.WriteLine();
                }
            }

            if (dataTable == null || dataTable.Rows.Count == 0)
            {
                throw new InvalidOperationException("未查询到数据");
            }

            // 将DataTable转换为Dictionary列表
            var rawData = new List<Dictionary<string, object>>();
            foreach (DataRow row in dataTable.Rows)
            {
                var dict = new Dictionary<string, object>();
                foreach (DataColumn column in dataTable.Columns)
                {
                    dict[column.ColumnName] = row[column] == DBNull.Value ? null : row[column];
                }
                rawData.Add(dict);
            }

            // 4) 生成Excel文件
            return GenerateExcelFile(rawData, request);
        }

        private (byte[] Bytes, string FileName, string ContentType) GenerateExcelFile(
            List<Dictionary<string, object>> rawData,
            DynamicUnifiedDailyReportRequestDto request)
        {
            using var wb = new XLWorkbook();
            var ws = wb.Worksheets.Add("营业报表");

            // 解析数据结构
            var processedData = ProcessDynamicData(rawData);

            // 生成表头和数据
            GenerateExcelHeaders(ws, processedData, request.Lang);
            GenerateExcelData(ws, processedData, request.Lang);

            // 设置样式
            ApplyExcelStyles(ws, processedData);

            // 保存文件
            using var ms = new MemoryStream();
            wb.SaveAs(ms);
            var bytes = ms.ToArray();

            var fileName = $"营业报表_{request.BeginDate}_to_{request.EndDate}.xlsx";
            return (bytes, fileName, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        }

        private ProcessedReportData ProcessDynamicData(List<Dictionary<string, object>> rawData)
        {
            var result = new ProcessedReportData
            {
                BaseData = new List<Dictionary<string, object>>(),
                TimeSlots = new List<TimeSlotInfo>()
            };

            var timeSlotFields = new Dictionary<string, Dictionary<string, List<object>>>();

            foreach (var row in rawData)
            {
                var baseRow = new Dictionary<string, object>();
                var timeSlotRow = new Dictionary<string, Dictionary<string, object>>();

                foreach (var kvp in row)
                {
                    // 匹配时间段字段格式: "HH:MM-HH:MM_字段名"
                    var timeMatch = Regex.Match(kvp.Key, @"^(\d{2}:\d{2}-\d{2}:\d{2})_(.+)$");

                    if (timeMatch.Success)
                    {
                        var timeSlot = timeMatch.Groups[1].Value;
                        var fieldName = timeMatch.Groups[2].Value;

                        if (!timeSlotFields.ContainsKey(timeSlot))
                            timeSlotFields[timeSlot] = new Dictionary<string, List<object>>();

                        if (!timeSlotFields[timeSlot].ContainsKey(fieldName))
                            timeSlotFields[timeSlot][fieldName] = new List<object>();

                        timeSlotFields[timeSlot][fieldName].Add(kvp.Value ?? 0);
                    }
                    else
                    {
                        baseRow[kvp.Key] = kvp.Value;
                    }
                }

                result.BaseData.Add(baseRow);
            }

            // 转换时间段数据
            foreach (var timeSlot in timeSlotFields.Keys.OrderBy(x => x))
            {
                result.TimeSlots.Add(new TimeSlotInfo
                {
                    TimeSlot = timeSlot,
                    Fields = timeSlotFields[timeSlot]
                });
            }

            return result;
        }

        private void GenerateExcelHeaders(IXLWorksheet ws, ProcessedReportData data, string lang)
        {
            var shopName = data.BaseData.FirstOrDefault()?.ContainsKey("ShopName") == true ?
                data.BaseData.First()["ShopName"]?.ToString() ?? "门店" : "门店";
            var dateRange = GetDateRangeFromData(data);

            // 第1行：标题行
            var title = $"【{shopName}】{dateRange}";
            ws.Cell(1, 1).Value = title;
            ws.Cell(1, 1).Style.Font.Bold = true;
            ws.Cell(1, 1).Style.Font.FontSize = 14;

            // 获取所有字段的分层结构
            var fieldStructure = GetFieldStructure(data);

            // 生成分层表头
            GenerateHierarchicalHeaders(ws, fieldStructure);
        }

        private Dictionary<string, object> GetFieldStructure(ProcessedReportData data)
        {
            var structure = new Dictionary<string, object>();
            var fieldMappings = GetFieldMappings();

            // 收集所有字段
            var allFields = new HashSet<string>();

            // 基础字段
            foreach (var row in data.BaseData)
            {
                foreach (var key in row.Keys)
                {
                    allFields.Add(key);
                }
            }

            // 时间段字段
            foreach (var timeSlot in data.TimeSlots)
            {
                foreach (var field in timeSlot.Fields.Keys)
                {
                    allFields.Add($"{timeSlot.TimeSlot}_{field}");
                }
            }

            // 构建分层结构
            foreach (var field in allFields)
            {
                if (fieldMappings.ContainsKey(field))
                {
                    var chineseName = fieldMappings[field];
                    var parts = chineseName.Split('_');

                    if (parts.Length == 1)
                    {
                        // 顶级字段
                        structure[field] = chineseName;
                    }
                    else
                    {
                        // 分层字段
                        var current = structure;
                        for (int i = 0; i < parts.Length - 1; i++)
                        {
                            var groupName = parts[i];
                            if (!current.ContainsKey(groupName))
                            {
                                current[groupName] = new Dictionary<string, object>();
                            }
                            current = (Dictionary<string, object>)current[groupName];
                        }
                        current[field] = parts[parts.Length - 1];
                    }
                }
            }

            return structure;
        }

        private void GenerateHierarchicalHeaders(IXLWorksheet ws, Dictionary<string, object> structure)
        {
            var headerRows = 3; // 使用3行表头
            var col = 1;

            // 递归生成表头
            GenerateHeaderLevel(ws, structure, 2, ref col, headerRows);

            // 设置表头样式
            ApplyHeaderStyles(ws, headerRows, col - 1);
        }

        private void GenerateHeaderLevel(IXLWorksheet ws, Dictionary<string, object> structure, int currentRow, ref int col, int maxRows)
        {
            foreach (var kvp in structure)
            {
                if (kvp.Value is Dictionary<string, object> subStructure)
                {
                    // 这是一个分组
                    var startCol = col;
                    var groupName = kvp.Key;

                    // 递归处理子级
                    GenerateHeaderLevel(ws, subStructure, currentRow + 1, ref col, maxRows);

                    // 设置分组标题并合并单元格
                    var endCol = col - 1;
                    if (endCol >= startCol)
                    {
                        ws.Cell(currentRow, startCol).Value = groupName;
                        if (endCol > startCol)
                        {
                            ws.Range(currentRow, startCol, currentRow, endCol).Merge();
                        }
                    }
                }
                else
                {
                    // 这是一个具体字段
                    var fieldName = kvp.Value.ToString();
                    ws.Cell(maxRows, col).Value = fieldName;

                    // 如果不是最后一行，需要合并到最后一行
                    if (currentRow < maxRows)
                    {
                        ws.Range(currentRow, col, maxRows, col).Merge();
                        ws.Cell(currentRow, col).Value = fieldName;
                    }

                    col++;
                }
            }
        }

        private Dictionary<string, string> GetFieldMappings()
        {
            return new Dictionary<string, string>
            {
                // 基础字段
                { "ReportDate", "日期" },
                { "ShopName", "门店" },
                { "Weekday", "星期" },
                { "Revenue_Total", "营收数据_总营业收入" },
                { "Revenue_DayTime", "营收数据_白天档" },
                { "Revenue_NightTime", "营收数据_晚档" },
                { "TotalBatchCount_AllDay", "带客数据_全天总营客批数" },
                { "DayTime_TotalBatch", "带客数据_白天档K+餐" },
                { "DayTime_DirectFallBatch", "带客数据_白天档直落" },
                { "NightTime_TotalBatch", "带客数据_20点后进场" },
                { "NightTime_DirectFallBatch", "带客数据_晚档直落" },
                { "BuffetGuestCount", "用餐人数_K+餐人数" },
                { "TotalDirectFallGuests", "用餐人数_直落人数" },

                // K+餐相关字段
                { "DayTime_KPlusMeal_BatchCount", "k+餐数据_k+餐批次" },
                { "DayTime_KPlusMeal_DirectFallBatch", "k+餐数据_k+餐直落批数" },
                { "NightTime_EarlySlots_DirectFallBatch", "k+餐数据_17点18点19点档直落" },

                // K+自由餐字段
                { "Night_FreeMeal_KPlus", "k+自由餐_k+" },
                { "Night_FreeMeal_SpecialReservation", "k+自由餐_特权预约" },
                { "Night_FreeMeal_Meituan", "k+自由餐_美团" },
                { "Night_FreeMeal_Douyin", "k+自由餐_抖音" },
                { "Night_FreeMeal_SubtotalBatch", "k+自由餐_小计" },
                { "Night_FreeMeal_Revenue", "k+自由餐_营业额" },

                // 20点后字段
                { "After8PM_BuyoutBatch", "20点后_买断批次" },
                { "After8PM_AllYouCanDrinkPackageBatch", "20点后_畅饮套餐" },
                { "After8PM_FreeConsumptionPackageBatch", "20点后_自由消套餐" },
                { "After8PM_Promo_SpecialReservation", "20点后_促销套餐_特权预约" },
                { "After8PM_Promo_Meituan", "20点后_促销套餐_美团" },
                { "After8PM_Promo_Douyin", "20点后_促销套餐_抖音" },
                { "After8PM_RoomFeeBatch", "20点后_房费批次" },
                { "After8PM_OtherBatch", "20点后_其他批次" },
                { "After8PM_SubtotalBatch", "20点后_批次小计" },
                { "After8PM_Revenue", "20点后_营收金额" },

                // 招待字段
                { "Complimentary_BatchCount", "招待_招待批次" },
                { "Complimentary_Revenue", "招待_招待金额" },

                // 时间段字段映射
                { "KPlus", "K+" },
                { "SpecialReservation", "特权预约" },
                { "Meituan", "美团" },
                { "Douyin", "抖音" },
                { "RoomFee", "房费" },
                { "Subtotal", "小计" },
                { "PrevSlotDirectFall", "上一档直落批数" }
            };
        }

        private void GenerateExcelData(IXLWorksheet ws, ProcessedReportData data, string lang)
        {
            int startRow = 4; // 从第4行开始写数据（前3行是表头）
            var fieldMappings = GetFieldMappings();

            for (int i = 0; i < data.BaseData.Count; i++)
            {
                int row = startRow + i;
                int col = 1;

                var baseRow = data.BaseData[i];

                // 按照字段映射的顺序写入数据
                foreach (var mapping in fieldMappings)
                {
                    var englishField = mapping.Key;
                    var value = "";

                    // 检查是否是时间段字段
                    if (IsTimeSlotField(englishField))
                    {
                        // 处理时间段字段
                        value = GetTimeSlotFieldValue(data, englishField, i);
                    }
                    else if (baseRow.ContainsKey(englishField))
                    {
                        // 处理基础字段
                        value = baseRow[englishField]?.ToString() ?? "";

                        // 特殊处理日期格式
                        if (englishField == "ReportDate" && !string.IsNullOrEmpty(value))
                        {
                            value = FormatReportDate(value);
                        }
                    }

                    ws.Cell(row, col).Value = value;
                    col++;
                }
            }
        }

        private bool IsTimeSlotField(string fieldName)
        {
            var timeSlotFields = new[] { "KPlus", "SpecialReservation", "Meituan", "Douyin", "RoomFee", "Subtotal", "PrevSlotDirectFall" };
            return timeSlotFields.Contains(fieldName);
        }

        private string GetTimeSlotFieldValue(ProcessedReportData data, string fieldName, int rowIndex)
        {
            // 在所有时间段中查找该字段的值
            foreach (var timeSlot in data.TimeSlots)
            {
                if (timeSlot.Fields.ContainsKey(fieldName) && timeSlot.Fields[fieldName].Count > rowIndex)
                {
                    return timeSlot.Fields[fieldName][rowIndex]?.ToString() ?? "0";
                }
            }
            return "0";
        }

        private void ApplyExcelStyles(IXLWorksheet ws, ProcessedReportData data)
        {
            // 计算总列数 - 基于字段映射的数量
            var fieldMappings = GetFieldMappings();
            var totalCols = fieldMappings.Count;
            var totalRows = data.BaseData.Count + 3; // 包含3行表头

            // 设置边框
            ws.Range(1, 1, totalRows, totalCols).Style.Border.OutsideBorder = XLBorderStyleValues.Thin;
            ws.Range(1, 1, totalRows, totalCols).Style.Border.InsideBorder = XLBorderStyleValues.Thin;

            // 自动调整列宽
            ws.Columns().AdjustToContents();
        }

        private void ApplyHeaderStyles(IXLWorksheet ws, int headerRows, int totalCols)
        {
            // 设置表头样式
            for (int row = 1; row <= headerRows; row++)
            {
                for (int col = 1; col <= totalCols; col++)
                {
                    var cell = ws.Cell(row, col);
                    cell.Style.Font.Bold = true;
                    cell.Style.Alignment.Horizontal = XLAlignmentHorizontalValues.Center;
                    cell.Style.Alignment.Vertical = XLAlignmentVerticalValues.Center;
                    cell.Style.Fill.BackgroundColor = XLColor.LightGray;
                }
            }
        }



        private string GetDateRangeFromData(ProcessedReportData data)
        {
            if (data.BaseData.Count > 0)
            {
                var firstDate = data.BaseData.First().ContainsKey("ReportDate") ?
                    data.BaseData.First()["ReportDate"]?.ToString() : "";
                var lastDate = data.BaseData.Last().ContainsKey("ReportDate") ?
                    data.BaseData.Last()["ReportDate"]?.ToString() : "";

                if (!string.IsNullOrEmpty(firstDate))
                {
                    var formattedFirst = FormatReportDate(firstDate);
                    var formattedLast = !string.IsNullOrEmpty(lastDate) ? FormatReportDate(lastDate) : formattedFirst;

                    if (formattedFirst == formattedLast)
                    {
                        return formattedFirst;
                    }
                    else
                    {
                        return $"{formattedFirst} 至 {formattedLast}";
                    }
                }
            }
            return "营业报表";
        }

        private string FormatReportDate(string dateString)
        {
            if (string.IsNullOrEmpty(dateString))
                return "";

            // 处理 .NET 日期格式 /Date(timestamp)/
            if (dateString.Contains("/Date("))
            {
                var match = Regex.Match(dateString, @"/Date\((\d+)\)/");
                if (match.Success && long.TryParse(match.Groups[1].Value, out var timestamp))
                {
                    var dateTime = DateTimeOffset.FromUnixTimeMilliseconds(timestamp).DateTime;
                    return dateTime.ToString("yyyy-MM-dd");
                }
            }

            return dateString;
        }
    }
}
