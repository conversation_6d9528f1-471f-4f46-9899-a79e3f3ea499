﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using SqlSugar;

namespace HdProject.Domain.Entities.SaasPos.MarketingManagement.Evaluation
{
    /// <summary>
    /// 评价指标表模型
    /// </summary>
    [SugarTable("MK_EvaluationIndicator")]
    public class EvaluationIndicator
    {
        /// <summary>
        /// 指标ID，自增主键
        /// </summary>
        [SugarColumn(ColumnName = "IndicatorID", IsPrimaryKey = true, IsIdentity = true)]
        public int IndicatorId { get; set; }

        /// <summary>
        /// 指标名称，如菜品、服务
        /// </summary>
        public string IndicatorName { get; set; }

        /// <summary>
        /// 指标类型，比如星级评分、文字描述等
        /// </summary>
        public string? IndicatorType { get; set; }

        /// <summary>
        /// 指标描述，对该指标的说明
        /// </summary>
        public string? IndicatorDescription { get; set; }
        /// <summary>
        /// 是否必填
        /// </summary>
        public bool? IsRequired { get; set; }

    }

}
