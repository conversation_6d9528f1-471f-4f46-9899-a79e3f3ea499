using HdProject.Application.Services.Interfaces;
using HdProject.Common.Config;
using HdProject.Common.DTOs;
using HdProject.Domain.Entities;
using Microsoft.Extensions.Options;
using Microsoft.IdentityModel.Tokens;
using System;
using System.IdentityModel.Tokens.Jwt;
using System.Linq;
using System.Security.Claims;
using System.Security.Cryptography;
using System.Text;

namespace HdProject.Application.Services
{
    public class JwtService : IJwtService
    {
        private readonly JwtSettings _jwtSettings;

        public JwtService(IOptions<JwtSettings> jwtSettings)
        {
            _jwtSettings = jwtSettings.Value;
        }

        public TokenResponseDto GenerateToken(User user)
        {
            var securityKey = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(_jwtSettings.SecretKey));
            var credentials = new SigningCredentials(securityKey, SecurityAlgorithms.HmacSha256);

            var roles = !string.IsNullOrEmpty(user.Roles)
                ? user.Roles.Split(',')
                : new string[] { };

            var claims = new[]
            {
                new Claim(JwtRegisteredClaimNames.Sub, user.Id.ToString()),
                new Claim(JwtRegisteredClaimNames.UniqueName, user.UserName),
                new Claim(JwtRegisteredClaimNames.Jti, Guid.NewGuid().ToString()),
                new Claim(ClaimTypes.Name, user.UserName)
            }.Concat(roles.Select(role => new Claim(ClaimTypes.Role, role))).ToArray();

            var accessTokenExpires = DateTime.UtcNow.AddMinutes(_jwtSettings.AccessTokenExpirationMinutes);
            var refreshTokenExpires = DateTime.UtcNow.AddDays(_jwtSettings.RefreshTokenExpirationDays);

            var accessToken = new JwtSecurityToken(
                issuer: _jwtSettings.Issuer,
                audience: _jwtSettings.Audience,
                claims: claims,
                expires: accessTokenExpires,
                signingCredentials: credentials
            );

            var refreshClaims = new[]
            {
                new Claim(JwtRegisteredClaimNames.Sub, user.Id.ToString()),
                new Claim(JwtRegisteredClaimNames.Jti, Guid.NewGuid().ToString())
            };

            var refreshToken = new JwtSecurityToken(
                issuer: _jwtSettings.Issuer,
                audience: _jwtSettings.Audience,
                claims: refreshClaims,
                expires: refreshTokenExpires,
                signingCredentials: credentials
            );

            return new TokenResponseDto
            {
                AccessToken = new JwtSecurityTokenHandler().WriteToken(accessToken),
                ExpiresAt = accessTokenExpires,
                UserName = user.UserName,
                Roles = roles,
                RefreshToken = new JwtSecurityTokenHandler().WriteToken(refreshToken),
                RefreshTokenExpiresAt = refreshTokenExpires
            };
        }

        public ClaimsPrincipal ValidateToken(string token)
        {
            return ValidateTokenInternal(token, true);
        }

        public ClaimsPrincipal ValidateRefreshToken(string refreshToken)
        {
            return ValidateTokenInternal(refreshToken, false);
        }

        private ClaimsPrincipal ValidateTokenInternal(string token, bool validateLifetime)
        {
            var tokenHandler = new JwtSecurityTokenHandler();
            var key = Encoding.ASCII.GetBytes(_jwtSettings.SecretKey);

            var validationParameters = new TokenValidationParameters
            {
                ValidateIssuerSigningKey = true,
                IssuerSigningKey = new SymmetricSecurityKey(key),
                ValidateIssuer = true,
                ValidIssuer = _jwtSettings.Issuer,
                ValidateAudience = true,
                ValidAudience = _jwtSettings.Audience,
                ValidateLifetime = validateLifetime,
                ClockSkew = TimeSpan.Zero
            };

            try
            {
                var principal = tokenHandler.ValidateToken(token, validationParameters, out _);
                return principal;
            }
            catch
            {
                return null;
            }
        }

        public async Task<ClaimsPrincipal> RefreshClaims(ClaimsPrincipal principal)
        {
            // 提取用户信息
            var userIdClaim = principal.FindFirst(JwtRegisteredClaimNames.Sub);
            if (userIdClaim == null || !int.TryParse(userIdClaim.Value, out int userId))
            {
                return null;
            }

            // 这里可以添加逻辑以从数据库或其他服务获取最新的用户信息
            // 例如，通过依赖注入一个用户服务来获取最新的用户角色和声明
            // 由于当前实现中没有用户服务，我们将返回原始 principal
            // 在实际应用中，可以添加更多逻辑来刷新声明

            return principal;
        }
        public string GenerateEncryptedQrCodeToken(string qrCodeData)
        {
            // 1. 准备签名密钥（将配置的字符串转为安全密钥）
            var signingKey = new SymmetricSecurityKey(
                Encoding.UTF8.GetBytes(_jwtSettings.SecretKey));

            // 2. 准备加密密钥（必须是16字节）
            var encryptionKey = new SymmetricSecurityKey(
                Encoding.UTF8.GetBytes(_jwtSettings.EncryptionKey));

            // 3. 最小化 Token 配置
            var handler = new JwtSecurityTokenHandler
            {
                SetDefaultTimesOnTokenCreation = false // 禁用自动时间戳（节省空间）
            };

            var tokenDescriptor = new SecurityTokenDescriptor
            {
                Subject = new ClaimsIdentity(new[] {
                new Claim("d", qrCodeData) // 只保留必要数据（用短字段名 "d"）
            }),
                Expires = DateTime.UtcNow.AddSeconds(_jwtSettings.QrCodeExpirationSeconds),
                SigningCredentials = new SigningCredentials(
                    signingKey,
                    SecurityAlgorithms.HmacSha256), // 必须用 HS256
                EncryptingCredentials = new EncryptingCredentials(
                    encryptionKey,
                    SecurityAlgorithms.Aes128KW,      // AES-128 密钥包装
                    SecurityAlgorithms.Aes128CbcHmacSha256) // AES-128 加密
            };

            return handler.WriteToken(handler.CreateToken(tokenDescriptor));

            //// 1. 准备签名密钥（将配置的字符串转为安全密钥）
            //var signingKey = new SymmetricSecurityKey(
            //    Encoding.UTF8.GetBytes(_jwtSettings.SecretKey));

            //// 2. 准备加密密钥（必须是16字节）
            //var encryptionKey = new SymmetricSecurityKey(
            //    Encoding.UTF8.GetBytes(_jwtSettings.EncryptionKey));

            //// 2. 手动构造最短 Header（无空格，字段名用单字母）
            //var header = @"{""a"":""HS256"",""e"":""A128GCM""}"; // "a"=alg, "e"=enc
            //var headerBytes = Encoding.UTF8.GetBytes(header);
            //var encodedHeader = Convert.ToBase64String(headerBytes).Replace("+", "-").Replace("/", "_").Replace("=", "");

            //// 3. 构造最短 Payload（字段名用单字母）
            //var expires = DateTimeOffset.UtcNow.AddSeconds(_jwtSettings.QrCodeExpirationSeconds).ToUnixTimeSeconds();
            //var payload = $@"{{""d"":""{qrCodeData}"",""exp"":{expires}}}"; // 添加 expy
            //var payloadBytes = Encoding.UTF8.GetBytes(payload);

            //// 4. AES-GCM 加密（内联实现，无辅助方法）
            //var iv = new byte[12]; // GCM推荐12字节IV
            //RandomNumberGenerator.Fill(iv);
            //var encrypted = new byte[payloadBytes.Length];
            //var tag = new byte[16];
            //using (var aes = new AesGcm(encryptionKey.Key))
            //{
            //    aes.Encrypt(iv, payloadBytes, encrypted, tag);
            //}
            //var encodedIv = Convert.ToBase64String(iv).Replace("+", "-").Replace("/", "_").Replace("=", "");
            //var encodedData = Convert.ToBase64String(encrypted).Replace("+", "-").Replace("/", "_").Replace("=", "");

            //// 5. HMAC-SHA256 签名（内联实现）
            //var signingContent = Encoding.UTF8.GetBytes(encodedHeader + "." + encodedIv + "." + encodedData);
            //var signature = HMACSHA256.HashData(signingKey.Key, signingContent);
            //var encodedSig = Convert.ToBase64String(signature).Replace("+", "-").Replace("/", "_").Replace("=", "");

            //// 6. 拼接最终 Token（格式：Header.IV.EncryptedData.Signature）
            //return $"{encodedHeader}.{encodedIv}.{encodedData}.{encodedSig}";

        }

    }
}