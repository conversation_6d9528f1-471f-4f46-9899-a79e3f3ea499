using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using HdProject.Domain.Result.Page;

namespace HdProject.Domain.Context
{
    internal class ShopBookLeaderContext
    {
    }
    public class RegisterLeaderContext
    {
        /// <summary>
        /// 用户ID (系统现有用户ID)
        /// </summary>
        public string UserId { get; set; }

        /// <summary>
        /// 团长姓名 (对应BookLeaderInfo.Name)
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// 联系电话 (对应BookLeaderInfo.Phone)
        /// </summary>
        public string Phone { get; set; }

        /// <summary>
        /// 银行卡号 (对应BookLeaderInfo.BankCard)
        /// </summary>
        //public string? BankCard { get; set; }

    }
    /// <summary>
    /// 获取团长个人信息请求参数
    /// </summary>
    public class GetLeaderPersonalInfoContext
    {

        public string UserId { get; set; }



    }

    /// <summary>
    /// 获取团长邀请码信息请求参数
    /// </summary>
    public class GetLeaderInvitationCodeContext
    {

        public string UserId { get; set; }

    }

    /// <summary>
    /// 团长提现申请请求参数
    /// </summary>
    public class ApplyForLeaderWithdrawalContext
    {

        public string UserId { get; set; }

        /// <summary>
        /// 提现金额 (对应BookLeaderWithdrawRecord.Amount)
        /// </summary>
        public decimal Amount { get; set; }

        /// <summary>
        /// 银行卡号 (对应BookLeaderWithdrawRecord.BankCard)
        /// </summary>
        public string BankCard { get; set; }

        /// <summary>
        /// 提现标题/备注 (对应BookLeaderWithdrawRecord.Title)
        /// </summary>
        public string Title { get; set; }


    }

    /// <summary>
    /// 获取团长预约记录列表请求参数
    /// </summary>
    public class GetLeaderAppointmentRecordsContext
    {
        /// <summary>
        /// 团长ID (对应BookLeaderInfo.LeaderId)
        /// </summary>
        public string UserId { get; set; }
        public int? Status { get; set; }
        public DateTime? AllDates { get; set; }
        public Pagination Paging { get; set; }

    }

    /// <summary>
    /// 获取团长提现记录列表请求参数
    /// </summary>
    public class GetLeaderWithdrawalRecordsContext
    {

        public string UserId { get; set; }
        public int? Status { get; set; }
        public DateTime? AllDates { get; set; }
        /// <summary>
        /// 分页
        /// </summary>
        public Pagination Paging { get; set; }


    }
    /// <summary>
    ///  汇总报表请求参数
    /// </summary>
    //public class GetSummaryReportContext
    //{
    //    /// <summary>
    //    /// 筛选订单的开始时间（基于订单创建时间）
    //    /// </summary>
    //    public DateTime? StartTime { get; set; }

    //    /// <summary>
    //    /// 筛选订单的结束时间（基于订单创建时间）
    //    /// </summary>
    //    public DateTime? EndTime { get; set; }
       
    //    /// <summary>
    //    /// 分页
    //    /// </summary>
    //    public Pagination Paging { get; set; }

    //}
    /// <summary>
    /// 团长信息请求参数
    /// </summary>
    public class GetLeaderInfoListContext
    {
        /// <summary>
        /// 团长注册的开始时间
        /// </summary>
        public DateTime? RegisterStartTime { get; set; }

        /// <summary>
        /// 团长注册的结束时间
        /// </summary>
        public DateTime? RegisterEndTime { get; set; }

        /// <summary>
        /// 分页
        /// </summary>
        public Pagination Paging { get; set; }
    }
    /// <summary>
    /// 团长订单信息请求参数
    /// </summary>
    public class GetOrderInfoListContext
    {
        /// <summary>
        /// 订单状态（0=未消费, 1=已消费, 2=退款）
        /// </summary>
        public int? OrderStatus { get; set; }

        /// <summary>
        /// 结算状态（0=未结算, 1=已结算）
        /// </summary>
        public int? SettlementStatus { get; set; }

        /// <summary>
        /// 下单开始时间（筛选时间范围）
        /// </summary>
        public DateTime? OrderStartTime { get; set; }

        /// <summary>
        /// 下单结束时间（筛选时间范围）
        /// </summary>
        public DateTime? OrderEndTime { get; set; }

        /// <summary>
        /// 分页
        /// </summary>
        public Pagination Paging { get; set; }
    }
    /// <summary>
    /// 团长佣金计提请求参数
    /// </summary>
    public class GetCommissionRecordsContext
    {
        
        /// <summary>
        /// 结算状态（0=未结算, 1=已结算）
        /// </summary>
        public int? SettlementStatus { get; set; }

        /// <summary>
        /// 消费开始时间（筛选时间范围）
        /// </summary>
        public DateTime? ConsumeStartTime { get; set; }

        /// <summary>
        /// 消费结束时间（筛选时间范围）
        /// </summary>
        public DateTime? ConsumeEndTime { get; set; }

        /// <summary>
        /// 分页
        /// </summary>
        public Pagination Paging { get; set; }
    }

}
